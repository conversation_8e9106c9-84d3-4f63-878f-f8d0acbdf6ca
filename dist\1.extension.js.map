{"version": 3, "file": "1.extension.js", "mappings": ";;;;;;;;;;;;;;AAAwC;AACc;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA,YAAY,QAAQ;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,YAAY,YAAY;AACxB;AACA;AACA;AACA;AACA;AACA,OAAO,0DAA0D;AACjE;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA,cAAc,aAAa;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,QAAQ;AACR;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kDAAkD,MAAM;AACxD;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA,4DAA4D,YAAY,YAAY;AACpF;AACA;AACA;;AAEA;AACA;AACA;AACA,oCAAoC,EAAE,EAAE;AACxC;AACA,EAAE;AACF;AACA;;AAEO;AACP;AACA;AACA;;AAEA,+CAA+C;;AAE/C;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,kEAAQ;;AAE9B;AACA,sCAAsC,aAAa;AACnD;;AAEA;AACA;AACA;;AAEA;AACA,mBAAmB,oDAAI,yBAAyB,kBAAkB;AAClE;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,uCAAuC,aAAa;AACpD;;AAEA;AACA,uCAAuC,aAAa;AACpD;;AAEA;AACA;AACA;;AAEA;AACA;AACA,4DAA4D,YAAY;;AAExE;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA", "sources": ["webpack://sia-ai-assistant/./node_modules/node-fetch/src/utils/multipart-parser.js"], "names": [], "sourceRoot": ""}