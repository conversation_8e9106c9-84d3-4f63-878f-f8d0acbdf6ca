export type WebviewToExtensionMessage = {
    type: 'userQuery';
    query?: string;
    payload?: string;
    mode?: 'chat' | 'agent' | 'auto-agent';
    model?: string;
} | {
    type: 'approvePlan';
    payload: {
        planId: string;
    };
} | {
    type: 'acceptDiff';
    payload: {
        diffId: string;
    };
} | {
    type: 'rejectDiff';
    payload: {
        diffId: string;
    };
} | {
    type: 'userInput';
    payload: {
        questionId: string;
        response: string;
    };
} | {
    type: 'stopAgent';
} | {
    type: 'newChat';
} | {
    type: 'setApiKey';
    payload: {
        provider: string;
        key: string;
    };
} | {
    type: 'getApiKeys';
} | {
    type: 'getCurrentModel';
} | {
    type: 'setModel';
    payload: {
        provider: string;
        model: string;
    };
} | {
    type: 'getModels';
} | {
    type: 'modeChanged';
    mode: string;
} | {
    type: 'tabChanged';
    tab: string;
};
export type ExtensionToWebviewMessage = {
    type: 'aiResponse';
    payload: string;
    mode: 'chat';
} | {
    type: 'planGenerated';
    payload: Plan;
    mode: 'agent';
} | {
    type: 'planStepUpdate';
    payload: PlanStep;
    mode: 'agent';
} | {
    type: 'showDiff';
    payload: {
        diffId: string;
        original: string;
        modified: string;
        filePath: string;
    };
    mode: 'agent';
} | {
    type: 'askUser';
    payload: {
        questionId: string;
        question: string;
    };
    mode: 'agent' | 'auto-agent';
} | {
    type: 'agentThought';
    payload: string;
    mode: 'auto-agent';
} | {
    type: 'agentAction';
    payload: {
        tool: string;
        params: any;
    };
    mode: 'auto-agent';
} | {
    type: 'agentResult';
    payload: string;
    mode: 'auto-agent';
} | {
    type: 'agentError';
    payload: string;
    mode: 'auto-agent' | 'all';
} | {
    type: 'agentStatus';
    payload: AgentState;
    mode: 'all';
} | {
    type: 'apiKeys';
    payload: {
        [provider: string]: boolean;
    };
    mode: 'all';
} | {
    type: 'models';
    payload: {
        available: string[];
        current: string;
    };
    mode: 'all';
} | {
    type: 'chatCleared';
} | {
    type: 'settingsShown';
    mode?: 'all';
} | {
    type: 'chatResponse';
    content: string;
    mode?: 'chat' | 'agent' | 'auto-agent';
} | {
    type: 'currentModel';
    payload: {
        provider: string;
        model: string;
    };
} | {
    type: 'error';
    error: string;
};
export interface Plan {
    id: string;
    steps: PlanStep[];
    description: string;
    status: 'pending' | 'approved' | 'executing' | 'completed' | 'failed';
}
export interface PlanStep {
    id: string;
    description: string;
    status: 'pending' | 'executing' | 'completed' | 'failed' | 'awaiting-approval';
    toolCall?: {
        tool: string;
        parameters: any;
    };
    result?: string;
    error?: string;
}
export declare enum AgentState {
    IDLE = "idle",
    PLANNING = "planning",
    AWAITING_PLAN_APPROVAL = "awaiting-plan-approval",
    EXECUTING_STEP = "executing-step",
    AWAITING_DIFF_APPROVAL = "awaiting-diff-approval",
    AWAITING_USER_INPUT = "awaiting-user-input",
    COMPLETED = "completed",
    ERROR = "error",
    STOPPED = "stopped"
}
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
    mode: 'chat' | 'agent' | 'auto-agent';
    model?: string;
    toolCalls?: any[];
    error?: string;
}
//# sourceMappingURL=messaging.d.ts.map