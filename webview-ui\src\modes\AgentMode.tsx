import React from 'react';
import { MessageList } from '../components/MessageList';

interface AgentModeProps {
    messages: any[];
    isLoading: boolean;
    onSendMessage: (content: string) => void;
}

export const AgentMode: React.FC<AgentModeProps> = ({ messages, isLoading, onSendMessage }) => {
    return (
        <div className="agent-mode">
            <div className="chat-area">
                {messages.length === 0 && !isLoading ? (
                    <div className="empty-state">
                        <div>
                            <h3>Agent Mode - Supervised Task Execution</h3>
                            <p>Provide a specific task and I'll create a plan for your approval.</p>
                            <div style={{ marginTop: '16px', fontSize: '12px', opacity: 0.7 }}>
                                <p>• I'll generate a detailed plan before taking action</p>
                                <p>• You'll approve each significant change</p>
                                <p>• I can modify files, run commands, and execute tests</p>
                                <p>• Perfect for refactoring, feature implementation, and debugging</p>
                            </div>
                        </div>
                    </div>
                ) : (
                    <MessageList messages={messages} isLoading={isLoading} />
                )}
            </div>
        </div>
    );
};
