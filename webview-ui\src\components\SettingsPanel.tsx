import React, { useState, useEffect } from 'react';
import { X, Key, Check, AlertCircle } from 'lucide-react';

interface SettingsPanelProps {
    onClose: () => void;
    vscode: any;
}

interface ApiKeyStatus {
    [provider: string]: boolean;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose, vscode }) => {
    const [apiKeys, setApiKeys] = useState<{ [provider: string]: string }>({});
    const [apiKeyStatus, setApiKeyStatus] = useState<ApiKeyStatus>({});
    const [isLoading, setIsLoading] = useState(false);
    const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

    const providers = [
        { id: 'gemini', name: 'Google Gemini', placeholder: 'AIza...' },
        { id: 'openai', name: 'OpenAI', placeholder: 'sk-...' },
        { id: 'anthropic', name: 'Anthropic', placeholder: 'sk-ant-...' },
        { id: 'mistral', name: 'Mistral AI', placeholder: 'API key...' },
        { id: 'deepseek', name: 'DeepSeek', placeholder: 'API key...' }
    ];

    useEffect(() => {
        // Request current API key status
        vscode.postMessage({ type: 'getApiKeys' });

        const handleMessage = (event: MessageEvent) => {
            const message = event.data;
            if (message.type === 'apiKeys') {
                setApiKeyStatus(message.payload);
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, [vscode]);

    const handleApiKeyChange = (provider: string, value: string) => {
        setApiKeys(prev => ({ ...prev, [provider]: value }));
    };

    const handleSaveApiKey = async (provider: string) => {
        const key = apiKeys[provider];
        if (!key || key.trim().length === 0) {
            setMessage({ type: 'error', text: 'API key cannot be empty' });
            return;
        }

        // Basic validation
        if (!validateApiKeyFormat(provider, key.trim())) {
            setMessage({ type: 'error', text: `Invalid API key format for ${provider}` });
            return;
        }

        setIsLoading(true);
        try {
            vscode.postMessage({
                type: 'setApiKey',
                payload: { provider, key: key.trim() }
            });

            // Update status optimistically
            setApiKeyStatus(prev => ({ ...prev, [provider]: true }));
            setApiKeys(prev => ({ ...prev, [provider]: '' }));
            setMessage({ type: 'success', text: `${provider.toUpperCase()} API key saved successfully` });
        } catch (error) {
            setMessage({ type: 'error', text: `Failed to save ${provider} API key` });
        } finally {
            setIsLoading(false);
        }
    };

    const validateApiKeyFormat = (provider: string, key: string): boolean => {
        switch (provider.toLowerCase()) {
            case 'gemini':
                return key.startsWith('AIza') && key.length > 30;
            case 'openai':
                return key.startsWith('sk-') && key.length > 40;
            case 'anthropic':
                return key.startsWith('sk-ant-') && key.length > 50;
            case 'mistral':
                return key.length > 20;
            case 'deepseek':
                return key.length > 20;
            default:
                return key.length > 10;
        }
    };

    const clearMessage = () => {
        setTimeout(() => setMessage(null), 3000);
    };

    useEffect(() => {
        if (message) {
            clearMessage();
        }
    }, [message]);

    return (
        <div className="settings-panel" style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'var(--vscode-editor-background)',
            zIndex: 1000,
            display: 'flex',
            flexDirection: 'column'
        }}>
            {/* Header */}
            <div className="settings-header" style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '16px',
                borderBottom: '1px solid var(--vscode-panel-border)'
            }}>
                <h2 style={{ margin: 0, fontSize: '16px', fontWeight: 600 }}>Settings</h2>
                <button
                    onClick={onClose}
                    style={{
                        background: 'none',
                        border: 'none',
                        color: 'var(--vscode-sideBar-foreground)',
                        cursor: 'pointer',
                        padding: '4px',
                        borderRadius: '2px'
                    }}
                >
                    <X size={16} />
                </button>
            </div>

            {/* Content */}
            <div className="settings-content" style={{
                flex: 1,
                padding: '24px',
                overflowY: 'auto'
            }}>
                {/* Message */}
                {message && (
                    <div className={`message ${message.type}`} style={{
                        marginBottom: '24px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                    }}>
                        {message.type === 'success' ? <Check size={16} /> : <AlertCircle size={16} />}
                        {message.text}
                    </div>
                )}

                {/* API Keys Section */}
                <div className="api-keys-section">
                    <h3 style={{ 
                        marginBottom: '16px', 
                        fontSize: '14px', 
                        fontWeight: 600,
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                    }}>
                        <Key size={16} />
                        API Keys
                    </h3>
                    <p style={{ 
                        marginBottom: '24px', 
                        fontSize: '12px', 
                        opacity: 0.8,
                        lineHeight: 1.4
                    }}>
                        Configure API keys for different AI providers. Keys are stored securely in VS Code's secret storage.
                    </p>

                    <div className="api-keys-list" style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                        {providers.map(provider => (
                            <div key={provider.id} className="api-key-item" style={{
                                padding: '16px',
                                backgroundColor: 'var(--vscode-editorWidget-background)',
                                border: '1px solid var(--vscode-editorWidget-border)',
                                borderRadius: '4px'
                            }}>
                                <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    marginBottom: '8px'
                                }}>
                                    <label style={{ 
                                        fontWeight: 600, 
                                        fontSize: '13px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px'
                                    }}>
                                        {provider.name}
                                        {apiKeyStatus[provider.id] && (
                                            <span style={{
                                                color: '#89d185',
                                                fontSize: '11px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '4px'
                                            }}>
                                                <Check size={12} />
                                                Configured
                                            </span>
                                        )}
                                    </label>
                                </div>
                                <div style={{ display: 'flex', gap: '8px' }}>
                                    <input
                                        type="password"
                                        placeholder={provider.placeholder}
                                        value={apiKeys[provider.id] || ''}
                                        onChange={(e) => handleApiKeyChange(provider.id, e.target.value)}
                                        style={{ flex: 1 }}
                                        disabled={isLoading}
                                    />
                                    <button
                                        onClick={() => handleSaveApiKey(provider.id)}
                                        disabled={isLoading || !apiKeys[provider.id]?.trim()}
                                        style={{ minWidth: '60px' }}
                                    >
                                        {isLoading ? '...' : 'Save'}
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Info Section */}
                <div className="info-section" style={{ marginTop: '32px' }}>
                    <h3 style={{ marginBottom: '16px', fontSize: '14px', fontWeight: 600 }}>
                        About SIA AI Assistant
                    </h3>
                    <div style={{ fontSize: '12px', opacity: 0.8, lineHeight: 1.4 }}>
                        <p>Version: 1.0.0</p>
                        <p>Multi-model AI coding assistant with intelligent tool orchestration.</p>
                        <p style={{ marginTop: '12px' }}>
                            Supports: Google Gemini, OpenAI GPT, Anthropic Claude, Mistral AI, and DeepSeek models.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};
