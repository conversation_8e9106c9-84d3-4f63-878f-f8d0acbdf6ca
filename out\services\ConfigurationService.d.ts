import * as vscode from 'vscode';
export interface SIAConfiguration {
    defaultModel: string;
    defaultProvider: string;
    maxTokens: number;
    temperature: number;
    enableLogging: boolean;
    enableTelemetry: boolean;
    autoSave: boolean;
    contextWindow: number;
    maxFileSize: number;
    excludedFileTypes: string[];
    enableCodeCompletion: boolean;
    enableRealTimeAnalysis: boolean;
    theme: 'dark' | 'light' | 'auto';
    panelPosition: 'side' | 'bottom' | 'right';
}
export declare class ConfigurationService {
    private static instance;
    private configuration;
    private constructor();
    static getInstance(): ConfigurationService;
    /**
     * Get the complete SIA configuration
     */
    getConfiguration(): SIAConfiguration;
    /**
     * Update a configuration value
     */
    updateConfiguration<K extends keyof SIAConfiguration>(key: K, value: SIAConfiguration[K], target?: vscode.ConfigurationTarget): Promise<void>;
    /**
     * Get a specific configuration value
     */
    get<K extends keyof SIAConfiguration>(key: K): SIAConfiguration[K];
    /**
     * Check if a file type should be excluded
     */
    isFileTypeExcluded(filePath: string): boolean;
    /**
     * Check if a file is too large to process
     */
    isFileTooLarge(fileSize: number): boolean;
    /**
     * Get the effective theme based on configuration and VS Code theme
     */
    getEffectiveTheme(): 'dark' | 'light';
    /**
     * Get model-specific configuration
     */
    getModelConfiguration(provider: string, model: string): {
        defaultModel: string;
        defaultProvider: string;
        maxTokens: number;
        temperature: number;
        enableLogging: boolean;
        enableTelemetry: boolean;
        autoSave: boolean;
        contextWindow: number;
        maxFileSize: number;
        excludedFileTypes: string[];
        enableCodeCompletion: boolean;
        enableRealTimeAnalysis: boolean;
        theme: "dark" | "light" | "auto";
        panelPosition: "side" | "bottom" | "right";
    };
    /**
     * Validate configuration values
     */
    validateConfiguration(): {
        isValid: boolean;
        errors: string[];
    };
    /**
     * Reset configuration to defaults
     */
    resetToDefaults(): Promise<void>;
    /**
     * Export configuration for backup
     */
    exportConfiguration(): string;
    /**
     * Import configuration from backup
     */
    importConfiguration(configJson: string): Promise<void>;
    /**
     * Get configuration schema for UI generation
     */
    getConfigurationSchema(): {
        defaultModel: {
            type: string;
            description: string;
            enum: string[];
        };
        defaultProvider: {
            type: string;
            description: string;
            enum: string[];
        };
        maxTokens: {
            type: string;
            description: string;
            minimum: number;
            maximum: number;
        };
        temperature: {
            type: string;
            description: string;
            minimum: number;
            maximum: number;
            step: number;
        };
        enableLogging: {
            type: string;
            description: string;
        };
        enableTelemetry: {
            type: string;
            description: string;
        };
        autoSave: {
            type: string;
            description: string;
        };
        contextWindow: {
            type: string;
            description: string;
            minimum: number;
            maximum: number;
        };
        maxFileSize: {
            type: string;
            description: string;
            minimum: number;
            maximum: number;
        };
        excludedFileTypes: {
            type: string;
            description: string;
            items: {
                type: string;
            };
        };
        enableCodeCompletion: {
            type: string;
            description: string;
        };
        enableRealTimeAnalysis: {
            type: string;
            description: string;
        };
        theme: {
            type: string;
            description: string;
            enum: string[];
        };
        panelPosition: {
            type: string;
            description: string;
            enum: string[];
        };
    };
    /**
     * Notify listeners of configuration changes
     */
    private notifyConfigurationChange;
}
//# sourceMappingURL=ConfigurationService.d.ts.map