<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIA - AI Assistant</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #1e1e1e;
            color: #cccccc;
            height: 100vh;
            overflow: hidden;
        }

        .app {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #1e1e1e;
            color: #cccccc;
        }

        /* Header */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background-color: #252526;
            border-bottom: 1px solid #3c3c3c;
            min-height: 48px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-title {
            font-weight: 400;
            font-size: 13px;
            color: #8c8c8c;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .new-chat-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .new-chat-dropdown {
            background-color: #3c3c3c;
            color: #cccccc;
            border: 1px solid #464647;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 13px;
            min-width: 120px;
            cursor: pointer;
        }

        .new-chat-dropdown:hover {
            background-color: #404040;
        }

        .new-chat-btn {
            background-color: #007acc;
            color: #ffffff;
            border: none;
            padding: 6px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 28px;
            height: 28px;
            transition: background-color 0.2s;
        }

        .new-chat-btn:hover {
            background-color: #1177bb;
        }

        .settings-btn {
            background: none;
            border: none;
            color: #cccccc;
            cursor: pointer;
            padding: 6px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }

        .settings-btn:hover {
            background-color: #2a2d2e;
        }

        /* Main content area */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-area {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .empty-state {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #cccccc;
            opacity: 0.7;
            font-size: 14px;
            text-align: center;
        }

        .empty-state h3 {
            color: #cccccc;
            margin-bottom: 8px;
            font-size: 18px;
            font-weight: 500;
        }

        .empty-state p {
            color: #8c8c8c;
            margin: 4px 0;
        }

        /* Message styles */
        .message {
            margin-bottom: 16px;
            display: flex;
            flex-direction: column;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .message-role {
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .message-role.user {
            color: #007acc;
        }

        .message-role.assistant {
            color: #00d4aa;
        }

        .message-content {
            background-color: #2d2d30;
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid #007acc;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .message.assistant .message-content {
            border-left-color: #00d4aa;
        }

        /* Bottom section */
        .bottom-section {
            border-top: 1px solid #3c3c3c;
            background-color: #252526;
            margin-top: auto;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #3c3c3c;
            padding: 0 16px;
        }

        .tab {
            padding: 12px 16px;
            background: none;
            border: none;
            color: #8c8c8c;
            cursor: pointer;
            font-size: 13px;
            border-bottom: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tab.active {
            color: #cccccc;
        }

        .tab:hover {
            background-color: #2a2d2e;
        }

        .beta-badge {
            background-color: #6f42c1;
            color: #ffffff;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            text-transform: uppercase;
            font-weight: 600;
        }

        /* Input area */
        .input-area {
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 12px;
            background-color: #1e1e1e;
        }

        .input-row {
            display: flex;
            align-items: flex-end;
            gap: 12px;
        }

        .input-container {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 44px;
            max-height: 120px;
            resize: none;
            padding: 12px 16px;
            border-radius: 6px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            background-color: #3c3c3c;
            border: 1px solid #464647;
            color: #cccccc;
            outline: none;
            transition: border-color 0.2s;
        }

        .message-input:focus {
            border-color: #007acc;
        }

        .message-input::placeholder {
            color: #8c8c8c;
            opacity: 1;
        }

        .agent-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .attach-btn {
            background-color: transparent;
            color: #8c8c8c;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            min-height: 44px;
            transition: all 0.2s;
        }

        .attach-btn:hover {
            background-color: #2a2d2e;
            color: #cccccc;
        }

        .agent-mode-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #cccccc;
        }

        .agent-mode-icon {
            color: #007acc;
            font-size: 16px;
        }

        .agent-mode-dropdown {
            background-color: #3c3c3c;
            color: #007acc;
            border: 1px solid #464647;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 13px;
            min-width: 80px;
            cursor: pointer;
            font-weight: 500;
        }

        .agent-mode-dropdown:hover {
            background-color: #404040;
        }

        .send-btn {
            background-color: transparent;
            color: #cccccc;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            min-height: 44px;
            transition: background-color 0.2s;
        }

        .send-btn:hover {
            background-color: #2a2d2e;
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Settings panel */
        .settings-panel {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #1e1e1e;
            z-index: 1000;
            padding: 20px;
            overflow-y: auto;
        }

        .settings-panel.active {
            display: block;
        }

        .settings-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #3c3c3c;
        }

        .settings-title {
            font-size: 18px;
            font-weight: 600;
            color: #cccccc;
        }

        .close-settings-btn {
            background: none;
            border: none;
            color: #cccccc;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            font-size: 16px;
        }

        .close-settings-btn:hover {
            background-color: #2a2d2e;
        }

        .settings-section {
            margin-bottom: 24px;
        }

        .settings-section h3 {
            color: #cccccc;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .api-key-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            padding: 12px;
            background-color: #252526;
            border-radius: 6px;
            border: 1px solid #3c3c3c;
        }

        .api-key-label {
            min-width: 80px;
            font-weight: 500;
            color: #cccccc;
        }

        .api-key-input {
            flex: 1;
            padding: 8px 12px;
            background-color: #3c3c3c;
            border: 1px solid #464647;
            border-radius: 4px;
            color: #cccccc;
            font-size: 13px;
        }

        .api-key-input:focus {
            outline: none;
            border-color: #007acc;
        }

        .api-key-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #666;
        }

        .api-key-status.valid {
            background-color: #00d4aa;
        }

        .api-key-status.invalid {
            background-color: #f14c4c;
        }

        .save-btn {
            background-color: #007acc;
            color: #ffffff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
        }

        .save-btn:hover {
            background-color: #1177bb;
        }

        /* Loading spinner */
        .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #3c3c3c;
            border-top: 2px solid #007acc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Hidden class */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="app">
        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <span class="header-title">SIA: CHAT</span>
            </div>
            <div class="header-right">
                <div class="new-chat-section">
                    <select class="new-chat-dropdown">
                        <option>New Chat</option>
                    </select>
                    <button class="new-chat-btn" title="New Chat">
                        ➕
                    </button>
                </div>
                <button class="settings-btn" title="Settings">
                    ⚙️
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="chat-area" id="chatArea">
                <div class="empty-state" id="emptyState">
                    <h3>Welcome to SIA AI Assistant</h3>
                    <p>Start a conversation or select some code to get help</p>
                    <p>Use @ mentions to include files, directories, or URLs</p>
                </div>
                <div class="messages" id="messages"></div>
            </div>
        </div>

        <!-- Bottom Section -->
        <div class="bottom-section">
            <div class="tabs">
                <button class="tab active" data-tab="memories">
                    📚 SIA Memories
                </button>
                <button class="tab" data-tab="extension">
                    🧩 extension
                    <span class="beta-badge">Beta</span>
                </button>
            </div>

            <!-- Input Area -->
            <div class="input-area">
                <div class="input-row">
                    <div class="input-container">
                        <textarea 
                            class="message-input" 
                            id="messageInput"
                            placeholder="Ask or instruct SIA Agent"
                            rows="1"
                        ></textarea>
                    </div>
                    <div class="agent-controls">
                        <button class="attach-btn" title="Attach context (@file, @url)">
                            @
                        </button>
                        <div class="agent-mode-selector">
                            <span class="agent-mode-icon">✏️</span>
                            <span>Agent</span>
                            <select class="agent-mode-dropdown" id="modeSelector">
                                <option value="chat">Chat</option>
                                <option value="agent">Agent</option>
                                <option value="auto-agent">Auto</option>
                            </select>
                        </div>
                        <button class="send-btn" id="sendBtn" title="Send message">
                            ➤
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Panel -->
        <div class="settings-panel" id="settingsPanel">
            <div class="settings-header">
                <h2 class="settings-title">SIA Settings</h2>
                <button class="close-settings-btn" id="closeSettingsBtn">✕</button>
            </div>

            <div class="settings-section">
                <h3>API Keys</h3>
                <div class="api-key-item">
                    <span class="api-key-label">Gemini</span>
                    <input type="password" class="api-key-input" data-provider="gemini" placeholder="Enter Gemini API key">
                    <div class="api-key-status" data-provider="gemini"></div>
                    <button class="save-btn" data-provider="gemini">Save</button>
                </div>
                <div class="api-key-item">
                    <span class="api-key-label">OpenAI</span>
                    <input type="password" class="api-key-input" data-provider="openai" placeholder="Enter OpenAI API key">
                    <div class="api-key-status" data-provider="openai"></div>
                    <button class="save-btn" data-provider="openai">Save</button>
                </div>
                <div class="api-key-item">
                    <span class="api-key-label">Anthropic</span>
                    <input type="password" class="api-key-input" data-provider="anthropic" placeholder="Enter Anthropic API key">
                    <div class="api-key-status" data-provider="anthropic"></div>
                    <button class="save-btn" data-provider="anthropic">Save</button>
                </div>
                <div class="api-key-item">
                    <span class="api-key-label">Mistral</span>
                    <input type="password" class="api-key-input" data-provider="mistral" placeholder="Enter Mistral API key">
                    <div class="api-key-status" data-provider="mistral"></div>
                    <button class="save-btn" data-provider="mistral">Save</button>
                </div>
                <div class="api-key-item">
                    <span class="api-key-label">DeepSeek</span>
                    <input type="password" class="api-key-input" data-provider="deepseek" placeholder="Enter DeepSeek API key">
                    <div class="api-key-status" data-provider="deepseek"></div>
                    <button class="save-btn" data-provider="deepseek">Save</button>
                </div>
            </div>

            <div class="settings-section">
                <h3>Model Selection</h3>
                <div class="api-key-item">
                    <span class="api-key-label">Provider</span>
                    <select class="api-key-input" id="providerSelector">
                        <option value="gemini">Google Gemini</option>
                        <option value="openai">OpenAI GPT</option>
                        <option value="anthropic">Anthropic Claude</option>
                        <option value="mistral">Mistral AI</option>
                        <option value="deepseek">DeepSeek</option>
                    </select>
                </div>
                <div class="api-key-item">
                    <span class="api-key-label">Model</span>
                    <select class="api-key-input" id="modelSelector">
                        <option value="gemini-2.0-flash-exp">Gemini 2.0 Flash</option>
                        <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                        <option value="gpt-4o">GPT-4o</option>
                        <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <script src="index.js"></script>
</body>
</html>
