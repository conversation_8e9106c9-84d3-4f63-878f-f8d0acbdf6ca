import * as vscode from 'vscode';
import { WebviewToExtensionMessage, ExtensionToWebviewMessage } from '../types/messaging';
export declare class WebviewPanelProvider {
    static currentPanel: WebviewPanelProvider | undefined;
    static readonly viewType = "siaAssistant";
    private readonly _panel;
    private readonly _extensionUri;
    private _disposables;
    private _messageHandler?;
    static createOrShow(extensionUri: vscode.Uri): WebviewPanelProvider;
    static revive(panel: vscode.WebviewPanel, extensionUri: vscode.Uri): void;
    private constructor();
    setMessageHandler(handler: (message: WebviewToExtensionMessage) => Promise<void>): void;
    sendMessage(message: ExtensionToWebviewMessage): void;
    newChat(): void;
    showSettings(): void;
    dispose(): void;
    private _update;
    private _getHtmlForWebview;
}
//# sourceMappingURL=WebviewPanelProvider.d.ts.map