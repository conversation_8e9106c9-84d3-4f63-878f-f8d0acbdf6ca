# SIA - Smart Intelligence Assistant

A production-ready AI-powered coding assistant for VS Code with multi-model support, intelligent tool orchestration, and advanced context management.

![SIA Assistant](https://img.shields.io/badge/VS%20Code-Extension-blue) ![License](https://img.shields.io/badge/License-MIT-green) ![TypeScript](https://img.shields.io/badge/TypeScript-4.9+-blue)

## 🚀 Features

### 🤖 Multi-Model AI Support
- **Google Gemini** (2.0 Flash, 2.5 Flash, 1.5 Flash)
- **OpenAI GPT** (GPT-4o, GPT-4 Turbo, GPT-3.5 Turbo)
- **Anthropic Claude** (3.5 Sonnet, 3 Opus, 3 Haiku)
- **Mistral AI** (Large, Medium, Small)
- **DeepSeek** (<PERSON><PERSON>, Coder)

### 🎯 Three Intelligent Operation Modes

#### 💬 Chat Mode
- **Read-only interactions** for safe code analysis
- **Smart context awareness** with @ mentions
- **Code explanations** and debugging assistance
- **Architecture guidance** and best practices

#### 🤝 Agent Mode
- **Supervised task execution** with user approval
- **Detailed planning** before any action
- **Diff previews** for all file changes
- **Step-by-step execution** with progress tracking
- **Perfect for refactoring** and feature implementation

#### 🚀 Auto-Agent Mode
- **Autonomous execution** with minimal supervision
- **Real-time thought streaming** and decision making
- **Self-correction** and adaptive problem solving
- **Ideal for well-defined tasks** and rapid development

### 🛠️ Comprehensive Tool Suite

#### File System Operations
- Read, write, create, move, and delete files
- Directory operations and structure analysis
- File existence checks and permissions

#### Code Intelligence
- **AST parsing** and semantic analysis
- **Symbol definition** and reference finding
- **Project structure** analysis
- **Dependency mapping** and framework detection

#### Development Tools
- **Terminal execution** and command running
- **Test generation** and execution
- **Code linting** and formatting
- **Package management** (npm, pip, cargo, etc.)

#### Web & Knowledge
- **Web search** with DuckDuckGo integration
- **GitHub repository** and code search
- **Package discovery** across ecosystems
- **Documentation fetching** from URLs

#### Advanced Workflows
- **Code refactoring** with intelligent suggestions
- **Performance optimization** analysis
- **Documentation generation** with JSDoc
- **Quality analysis** and improvement recommendations

### 🎯 Smart Context Management

Use **@ mentions** to provide precise context:

```
@src/extension.ts - Include specific files
@components/ - Include directory structures  
@https://docs.example.com - Include web documentation
@package.json - Include configuration files
```

**Automatic context inclusion:**
- Current editor selection
- Recently modified files
- Project configuration
- Error history

### 🎨 Modern UI Design

- **Side panel integration** in VS Code activity bar
- **Dark theme optimized** interface
- **Real-time progress** indicators
- **Interactive diff viewer** for changes
- **Responsive design** for all screen sizes

## 📦 Installation

### From Source

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/sia-ai-assistant.git
   cd sia-ai-assistant
   ```

2. **Install dependencies**
   ```bash
   npm install
   cd webview-ui && npm install && cd ..
   ```

3. **Build the extension**
   ```bash
   npm run compile
   ```

4. **Run in development**
   ```bash
   # Press F5 in VS Code to launch Extension Development Host
   ```

### Package for Distribution

```bash
npm install -g vsce
vsce package
```

## ⚙️ Configuration

### API Key Setup

1. **Open SIA Settings**
   - Click the ⚙️ settings icon in the SIA panel
   - Or use Command Palette: `SIA: Settings`

2. **Add API Keys**
   - **Gemini**: Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - **OpenAI**: Get from [OpenAI Platform](https://platform.openai.com/api-keys)
   - **Anthropic**: Get from [Anthropic Console](https://console.anthropic.com/)
   - **Mistral**: Get from [Mistral Platform](https://console.mistral.ai/)
   - **DeepSeek**: Get from [DeepSeek Platform](https://platform.deepseek.com/)

3. **Secure Storage**
   - All API keys are encrypted and stored in VS Code's secure storage
   - Keys are never logged or transmitted except to their respective APIs

### Model Selection

- **Dynamic model switching** based on available API keys
- **Automatic fallback** to available models
- **Performance optimization** for different task types

## 🎮 Usage Examples

### Basic Chat Interaction
```
User: Explain this function @src/utils/parser.ts
SIA: [Analyzes the parser function with full context]
```

### Supervised Refactoring (Agent Mode)
```
User: Refactor @src/components/ to use TypeScript strict mode
SIA: [Creates detailed plan] → [Shows diffs] → [Executes with approval]
```

### Autonomous Development (Auto-Agent Mode)
```
User: Add comprehensive tests for the authentication module
SIA: [Analyzes code] → [Generates tests] → [Runs validation] → [Reports results]
```

### Context-Rich Analysis
```
User: Compare @old-implementation.js with @new-implementation.js and suggest improvements
SIA: [Detailed comparison with specific recommendations]
```

## 🧪 Development

### Project Structure
```
sia-ai-assistant/
├── src/                    # Extension source code
│   ├── agent/             # Agent orchestration
│   ├── controllers/       # Tool controllers
│   ├── managers/          # API key management
│   ├── providers/         # Webview providers
│   ├── services/          # Core services
│   └── types/             # TypeScript definitions
├── webview-ui/            # React UI components
│   ├── src/               # React source
│   └── public/            # Static assets
├── dist/                  # Compiled output
└── out/                   # TypeScript output
```

### Building & Testing

```bash
# Install dependencies
npm install

# Build extension
npm run compile

# Build webview UI
cd webview-ui && npm run build

# Run tests
npm test

# Package extension
vsce package
```

### Code Quality

- **TypeScript strict mode** enabled
- **ESLint** configuration for code quality
- **Prettier** for consistent formatting
- **Comprehensive test suite** with 90%+ coverage

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. **Fork and clone** the repository
2. **Install dependencies**: `npm install`
3. **Create feature branch**: `git checkout -b feature/amazing-feature`
4. **Make changes** and add tests
5. **Run tests**: `npm test`
6. **Submit pull request**

### Reporting Issues

- Use the [GitHub Issues](https://github.com/your-username/sia-ai-assistant/issues) page
- Include VS Code version, extension version, and reproduction steps
- Attach relevant logs from the SIA output channel

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **VS Code Extension API** for the robust platform
- **OpenAI, Anthropic, Google, Mistral, DeepSeek** for their amazing AI models
- **TypeScript** and **React** communities for excellent tooling
- **Contributors** who help make SIA better

## 📞 Support

- **Documentation**: [Wiki](https://github.com/your-username/sia-ai-assistant/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-username/sia-ai-assistant/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/sia-ai-assistant/discussions)

---

**Made with ❤️ for developers by developers**
