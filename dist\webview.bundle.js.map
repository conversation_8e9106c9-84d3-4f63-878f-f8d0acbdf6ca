{"version": 3, "file": "webview.bundle.js", "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;;AAET;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,kCAAkC,oBAAoB;AACtD,kCAAkC,yBAAyB;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,kCAAkC,iBAAiB;AACnD;;AAEA;AACA,0BAA0B;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,0CAA0C,aAAa;;AAEvD;AACA;;AAEA;AACA,6CAA6C,aAAa;AAC1D;;AAEA;AACA;AACA;AACA,uCAAuC,aAAa;AACpD;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,8EAA8E,SAAS;AACvF;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB;AACvB,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,2FAA2F,SAAS;AACpG;AACA,4DAA4D,uCAAuC;AACnG;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB,yDAAyD;AAC3E,kBAAkB;AAClB;AACA;AACA,kBAAkB,iCAAiC;AACnD,kBAAkB,2CAA2C;AAC7D,kBAAkB;AAClB;AACA;AACA,kBAAkB,gEAAgE;AAClF,kBAAkB,wDAAwD;AAC1E,kBAAkB;AAClB;AACA;AACA,kBAAkB,sDAAsD;AACxE,kBAAkB;AAClB;AACA;AACA,kBAAkB,+CAA+C;AACjE,kBAAkB;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,4BAA4B;AAC3E;;AAEA;AACA;AACA;AACA,6CAA6C,IAAI;AACjD;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,2DAA2D,iBAAiB,cAAc,+BAA+B,MAAM,IAAI,iBAAiB,cAAc;AAClK;;AAEA;AACA;AACA;;AAEA;AACA,6CAA6C,iBAAiB,aAAa,iBAAiB,YAAY,eAAe;AACvH;;AAEA;AACA,kDAAkD,QAAQ;AAC1D;AACA;;AAEA;AACA;AACA;AACA,CAAC", "sources": ["webpack://sia-ai-assistant/./webview-ui/src/index.js"], "sourcesContent": ["// SIA AI Assistant - <PERSON>illa JS Webview\nclass SIAWebview {\n    constructor() {\n        this.vscode = acquireVsCodeApi();\n        this.currentMode = 'chat';\n        this.messages = [];\n        this.isLoading = false;\n        this.apiKeyStatus = {};\n        \n        this.initializeElements();\n        this.setupEventListeners();\n        this.setupMessageListener();\n        this.requestInitialData();\n    }\n\n    initializeElements() {\n        // Main elements\n        this.chatArea = document.getElementById('chatArea');\n        this.messagesContainer = document.getElementById('messages');\n        this.emptyState = document.getElementById('emptyState');\n        this.messageInput = document.getElementById('messageInput');\n        this.sendBtn = document.getElementById('sendBtn');\n        this.modeSelector = document.getElementById('modeSelector');\n        \n        // Header elements\n        this.newChatBtn = document.querySelector('.new-chat-btn');\n        this.settingsBtn = document.querySelector('.settings-btn');\n        this.headerTitle = document.querySelector('.header-title');\n        \n        // Settings elements\n        this.settingsPanel = document.getElementById('settingsPanel');\n        this.closeSettingsBtn = document.getElementById('closeSettingsBtn');\n        this.providerSelector = document.getElementById('providerSelector');\n        this.modelSelector = document.getElementById('modelSelector');\n        \n        // Tab elements\n        this.tabs = document.querySelectorAll('.tab');\n        \n        // API key elements\n        this.apiKeyInputs = document.querySelectorAll('.api-key-input[data-provider]');\n        this.apiKeyStatuses = document.querySelectorAll('.api-key-status[data-provider]');\n        this.saveBtns = document.querySelectorAll('.save-btn[data-provider]');\n    }\n\n    setupEventListeners() {\n        // Send message\n        this.sendBtn.addEventListener('click', () => this.handleSendMessage());\n        this.messageInput.addEventListener('keydown', (e) => {\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                this.handleSendMessage();\n            }\n        });\n\n        // Mode selector\n        this.modeSelector.addEventListener('change', (e) => {\n            this.currentMode = e.target.value;\n            this.updateHeaderTitle();\n            this.vscode.postMessage({\n                type: 'modeChanged',\n                mode: this.currentMode\n            });\n        });\n\n        // Header buttons\n        this.newChatBtn.addEventListener('click', () => this.handleNewChat());\n        this.settingsBtn.addEventListener('click', () => this.showSettings());\n\n        // Settings panel\n        this.closeSettingsBtn.addEventListener('click', () => this.hideSettings());\n\n        // API key management\n        this.saveBtns.forEach(btn => {\n            btn.addEventListener('click', (e) => {\n                const provider = e.target.dataset.provider;\n                this.handleSaveApiKey(provider);\n            });\n        });\n\n        // Provider/Model selection\n        this.providerSelector.addEventListener('change', () => this.updateModelOptions());\n        this.modelSelector.addEventListener('change', () => this.handleModelChange());\n\n        // Tabs\n        this.tabs.forEach(tab => {\n            tab.addEventListener('click', (e) => {\n                this.handleTabChange(e.target.dataset.tab);\n            });\n        });\n\n        // Auto-resize textarea\n        this.messageInput.addEventListener('input', () => this.autoResizeTextarea());\n    }\n\n    setupMessageListener() {\n        window.addEventListener('message', (event) => {\n            const message = event.data;\n            this.handleExtensionMessage(message);\n        });\n    }\n\n    requestInitialData() {\n        // Request API key status and current settings\n        this.vscode.postMessage({ type: 'getApiKeys' });\n        this.vscode.postMessage({ type: 'getCurrentModel' });\n    }\n\n    handleExtensionMessage(message) {\n        switch (message.type) {\n            case 'chatResponse':\n                this.handleChatResponse(message.content, message.mode);\n                break;\n            case 'chatCleared':\n                this.handleChatCleared();\n                break;\n            case 'settingsShown':\n                this.showSettings();\n                break;\n            case 'apiKeys':\n                this.updateApiKeyStatus(message.payload);\n                break;\n            case 'currentModel':\n                this.updateCurrentModel(message.payload);\n                break;\n            case 'error':\n                this.showError(message.error);\n                break;\n            case 'planGenerated':\n                this.handlePlanGenerated(message.plan);\n                break;\n            case 'planApproved':\n                this.handlePlanApproved();\n                break;\n            case 'stepCompleted':\n                this.handleStepCompleted(message.step, message.result);\n                break;\n            case 'agentThought':\n                this.handleAgentThought(message.thought);\n                break;\n        }\n    }\n\n    handleSendMessage() {\n        const content = this.messageInput.value.trim();\n        if (!content || this.isLoading) return;\n\n        // Add user message to UI\n        this.addMessage('user', content);\n        \n        // Clear input\n        this.messageInput.value = '';\n        this.autoResizeTextarea();\n\n        // Hide empty state\n        this.emptyState.style.display = 'none';\n\n        // Set loading state\n        this.setLoading(true);\n\n        // Send to extension\n        this.vscode.postMessage({\n            type: 'userQuery',\n            query: content,\n            mode: this.currentMode\n        });\n    }\n\n    handleChatResponse(content, mode) {\n        this.setLoading(false);\n        this.addMessage('assistant', content, mode);\n    }\n\n    handleChatCleared() {\n        this.messages = [];\n        this.messagesContainer.innerHTML = '';\n        this.emptyState.style.display = 'flex';\n    }\n\n    handleNewChat() {\n        this.vscode.postMessage({ type: 'newChat' });\n    }\n\n    addMessage(role, content, mode = null) {\n        const message = { role, content, timestamp: Date.now(), mode };\n        this.messages.push(message);\n\n        const messageElement = this.createMessageElement(message);\n        this.messagesContainer.appendChild(messageElement);\n        \n        // Scroll to bottom\n        this.chatArea.scrollTop = this.chatArea.scrollHeight;\n    }\n\n    createMessageElement(message) {\n        const messageDiv = document.createElement('div');\n        messageDiv.className = `message ${message.role}`;\n\n        const headerDiv = document.createElement('div');\n        headerDiv.className = 'message-header';\n\n        const roleSpan = document.createElement('span');\n        roleSpan.className = `message-role ${message.role}`;\n        roleSpan.textContent = message.role === 'user' ? 'You' : 'SIA';\n\n        if (message.mode) {\n            const modeSpan = document.createElement('span');\n            modeSpan.className = 'message-mode';\n            modeSpan.textContent = `(${message.mode})`;\n            modeSpan.style.color = '#8c8c8c';\n            modeSpan.style.fontSize = '11px';\n            headerDiv.appendChild(roleSpan);\n            headerDiv.appendChild(modeSpan);\n        } else {\n            headerDiv.appendChild(roleSpan);\n        }\n\n        const contentDiv = document.createElement('div');\n        contentDiv.className = 'message-content';\n        contentDiv.textContent = message.content;\n\n        messageDiv.appendChild(headerDiv);\n        messageDiv.appendChild(contentDiv);\n\n        return messageDiv;\n    }\n\n    setLoading(loading) {\n        this.isLoading = loading;\n        this.sendBtn.disabled = loading;\n        \n        if (loading) {\n            this.sendBtn.innerHTML = '<div class=\"loading-spinner\"></div>';\n        } else {\n            this.sendBtn.innerHTML = '➤';\n        }\n    }\n\n    showSettings() {\n        this.settingsPanel.classList.add('active');\n    }\n\n    hideSettings() {\n        this.settingsPanel.classList.remove('active');\n    }\n\n    handleSaveApiKey(provider) {\n        const input = document.querySelector(`.api-key-input[data-provider=\"${provider}\"]`);\n        const key = input.value.trim();\n        \n        if (!key) {\n            this.showError('API key cannot be empty');\n            return;\n        }\n\n        // Send to extension\n        this.vscode.postMessage({\n            type: 'setApiKey',\n            payload: { provider, key }\n        });\n\n        // Clear input\n        input.value = '';\n    }\n\n    updateApiKeyStatus(status) {\n        this.apiKeyStatus = status;\n        \n        Object.keys(status).forEach(provider => {\n            const statusElement = document.querySelector(`.api-key-status[data-provider=\"${provider}\"]`);\n            if (statusElement) {\n                statusElement.className = `api-key-status ${status[provider] ? 'valid' : 'invalid'}`;\n            }\n        });\n    }\n\n    updateCurrentModel(modelInfo) {\n        if (modelInfo.provider) {\n            this.providerSelector.value = modelInfo.provider;\n        }\n        if (modelInfo.model) {\n            this.updateModelOptions();\n            this.modelSelector.value = modelInfo.model;\n        }\n    }\n\n    updateModelOptions() {\n        const provider = this.providerSelector.value;\n        const modelOptions = {\n            gemini: [\n                { value: 'gemini-2.0-flash-exp', text: 'Gemini 2.0 Flash' },\n                { value: 'gemini-1.5-flash', text: 'Gemini 1.5 Flash' }\n            ],\n            openai: [\n                { value: 'gpt-4o', text: 'GPT-4o' },\n                { value: 'gpt-4-turbo', text: 'GPT-4 Turbo' },\n                { value: 'gpt-3.5-turbo', text: 'GPT-3.5 Turbo' }\n            ],\n            anthropic: [\n                { value: 'claude-3-5-sonnet-20241022', text: 'Claude 3.5 Sonnet' },\n                { value: 'claude-3-opus-20240229', text: 'Claude 3 Opus' },\n                { value: 'claude-3-haiku-20240307', text: 'Claude 3 Haiku' }\n            ],\n            mistral: [\n                { value: 'mistral-large-latest', text: 'Mistral Large' },\n                { value: 'mistral-medium-latest', text: 'Mistral Medium' }\n            ],\n            deepseek: [\n                { value: 'deepseek-chat', text: 'DeepSeek Chat' },\n                { value: 'deepseek-coder', text: 'DeepSeek Coder' }\n            ]\n        };\n\n        const options = modelOptions[provider] || [];\n        this.modelSelector.innerHTML = '';\n        \n        options.forEach(option => {\n            const optionElement = document.createElement('option');\n            optionElement.value = option.value;\n            optionElement.textContent = option.text;\n            this.modelSelector.appendChild(optionElement);\n        });\n    }\n\n    handleModelChange() {\n        const provider = this.providerSelector.value;\n        const model = this.modelSelector.value;\n        \n        this.vscode.postMessage({\n            type: 'setModel',\n            payload: { provider, model }\n        });\n    }\n\n    updateHeaderTitle() {\n        const modeNames = {\n            chat: 'CHAT',\n            agent: 'AGENT',\n            'auto-agent': 'AUTO-AGENT'\n        };\n        this.headerTitle.textContent = `SIA: ${modeNames[this.currentMode]}`;\n    }\n\n    handleTabChange(tab) {\n        // Update active tab\n        this.tabs.forEach(t => t.classList.remove('active'));\n        document.querySelector(`[data-tab=\"${tab}\"]`).classList.add('active');\n        \n        // Handle tab-specific logic\n        this.vscode.postMessage({\n            type: 'tabChanged',\n            tab: tab\n        });\n    }\n\n    autoResizeTextarea() {\n        this.messageInput.style.height = 'auto';\n        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';\n    }\n\n    showError(error) {\n        // Create a temporary error message\n        const errorDiv = document.createElement('div');\n        errorDiv.style.cssText = `\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            background-color: #f14c4c;\n            color: white;\n            padding: 12px 16px;\n            border-radius: 6px;\n            font-size: 14px;\n            z-index: 10000;\n            max-width: 300px;\n            word-wrap: break-word;\n        `;\n        errorDiv.textContent = error;\n        document.body.appendChild(errorDiv);\n\n        // Remove after 5 seconds\n        setTimeout(() => {\n            if (errorDiv.parentNode) {\n                errorDiv.parentNode.removeChild(errorDiv);\n            }\n        }, 5000);\n    }\n\n    // Agent mode specific handlers\n    handlePlanGenerated(plan) {\n        this.addMessage('assistant', `Plan Generated:\\n\\n${plan.description}\\n\\nSteps:\\n${plan.steps.map((step, i) => `${i + 1}. ${step.description}`).join('\\n')}\\n\\nApprove this plan?`, 'agent');\n    }\n\n    handlePlanApproved() {\n        this.addMessage('assistant', 'Plan approved. Executing steps...', 'agent');\n    }\n\n    handleStepCompleted(step, result) {\n        this.addMessage('assistant', `Step ${step.stepNumber} completed: ${step.description}\\nResult: ${result.message}`, 'agent');\n    }\n\n    handleAgentThought(thought) {\n        this.addMessage('assistant', `[THOUGHT] ${thought}`, 'auto-agent');\n    }\n}\n\n// Initialize the webview when DOM is loaded\ndocument.addEventListener('DOMContentLoaded', () => {\n    new SIAWebview();\n});\n"], "names": [], "sourceRoot": ""}