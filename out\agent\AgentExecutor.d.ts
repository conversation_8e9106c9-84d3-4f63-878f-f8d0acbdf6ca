import * as vscode from 'vscode';
import { LLMService } from '../services/LLMService';
import { ToolController } from '../controllers/ToolController';
import { WebviewPanelProvider } from '../providers/WebviewPanelProvider';
import { SidePanelProvider } from '../providers/SidePanelProvider';
import { WebviewToExtensionMessage } from '../types/messaging';
export declare class AgentExecutor {
    private llmService;
    private toolController;
    private webviewProvider;
    private contextManager;
    private currentState;
    private currentPlan?;
    private context;
    private memory;
    private conversationHistory;
    private projectInfo?;
    constructor(llmService: LLMService, toolController: ToolController, webviewProvider: WebviewPanelProvider | SidePanelProvider, extensionContext: vscode.ExtensionContext);
    /**
     * Handle messages from the webview
     */
    handleWebviewMessage(message: WebviewToExtensionMessage): Promise<void>;
    /**
     * Handle user query based on mode
     */
    private handleUserQuery;
    /**
     * Handle chat mode - read-only interactions
     */
    private handleChatMode;
    /**
     * Handle agent mode - supervised task execution
     */
    private handleAgentMode;
    /**
     * Handle auto-agent mode - autonomous execution
     */
    private handleAutoAgentMode;
    /**
     * Generate a plan for agent mode
     */
    private generatePlan;
    /**
     * Autonomous execution loop for auto-agent mode
     */
    private autonomousExecutionLoop;
    /**
     * Handle plan approval
     */
    private handlePlanApproval;
    /**
     * Handle diff approval/rejection
     */
    private handleDiffApproval;
    /**
     * Handle user input response
     */
    private handleUserInput;
    /**
     * Stop agent execution
     */
    private stopAgent;
    /**
     * Start new chat
     */
    private newChat;
    /**
     * Handle API key setting
     */
    private handleSetApiKey;
    /**
     * Handle get API keys request
     */
    private handleGetApiKeys;
    /**
     * Handle model setting
     */
    private handleSetModel;
    /**
     * Handle get models request
     */
    private handleGetModels;
    /**
     * Update agent state and notify webview
     */
    private updateState;
    /**
     * Send plan step update to webview
     */
    private sendPlanStepUpdate;
    /**
     * Send error message to webview
     */
    private sendErrorToWebview;
    /**
     * Get system prompt for chat mode
     */
    private getChatModeSystemPrompt;
    /**
     * Get system prompt for agent mode
     */
    private getAgentModeSystemPrompt;
    /**
     * Get system prompt for auto-agent mode
     */
    private getAutoAgentSystemPrompt;
    /**
     * Send message to webview
     */
    private sendToWebview;
    /**
     * Handle get current model request
     */
    private handleGetCurrentModel;
    /**
     * Handle mode changed
     */
    private handleModeChanged;
    /**
     * Handle tab changed
     */
    private handleTabChanged;
}
//# sourceMappingURL=AgentExecutor.d.ts.map