import * as vscode from 'vscode';
import { WebviewPanelProvider } from './providers/WebviewPanelProvider';
import { SidePanelProvider } from './providers/SidePanelProvider';
import { ApiKeyManager } from './managers/ApiKeyManager';
import { LLMService } from './services/LLMService';
import { ToolController } from './controllers/ToolController';
import { AgentExecutor } from './agent/AgentExecutor';
import { InlineEditingService } from './services/InlineEditingService';

let webviewProvider: WebviewPanelProvider;
let sidePanelProvider: SidePanelProvider;
let apiKeyManager: ApiKeyManager;
let llmService: LLMService;
let toolController: ToolController;
let agentExecutor: AgentExecutor;
let inlineEditingService: InlineEditingService;

export async function activate(context: vscode.ExtensionContext) {
    console.log('SIA AI Assistant is now active!');

    // Initialize core services
    apiKeyManager = new ApiKeyManager(context);
    llmService = new LLMService(apiKeyManager);
    toolController = new ToolController(context);
    
    // Initialize webview providers (don't auto-create webview panel)
    sidePanelProvider = new SidePanelProvider(context.extensionUri);

    // Register the side panel provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(SidePanelProvider.viewType, sidePanelProvider)
    );

    // Initialize agent executor
    agentExecutor = new AgentExecutor(llmService, toolController, sidePanelProvider, context);

    // Initialize inline editing service
    inlineEditingService = InlineEditingService.getInstance(llmService, toolController);

    // Set up message handling for side panel
    sidePanelProvider.setMessageHandler(agentExecutor.handleWebviewMessage.bind(agentExecutor));

    // Pass API key manager to side panel provider
    (sidePanelProvider as any).apiKeyManager = apiKeyManager;

  
    const startCommand = vscode.commands.registerCommand('sia.start', () => {
        // Create webview panel only when explicitly requested
        webviewProvider = WebviewPanelProvider.createOrShow(context.extensionUri);
        webviewProvider.setMessageHandler(agentExecutor.handleWebviewMessage.bind(agentExecutor));
        (webviewProvider as any).apiKeyManager = apiKeyManager;
    });

    const newChatCommand = vscode.commands.registerCommand('sia.newChat', () => {
        sidePanelProvider.newChat();
        if (webviewProvider) {
            webviewProvider.newChat();
        }
    });

    const settingsCommand = vscode.commands.registerCommand('sia.settings', () => {
        sidePanelProvider.showSettings();
        if (webviewProvider) {
            webviewProvider.showSettings();
        }
    });

    const refreshCommand = vscode.commands.registerCommand('sia.refresh', () => {
        // Refresh the side panel view
        vscode.commands.executeCommand('workbench.action.webview.reloadWebviewAction');
    });

    // Inline editing commands
    const inlineEditCommand = vscode.commands.registerCommand('sia.inlineEdit', async () => {
        await handleInlineEdit('edit');
    });

    const inlineRefactorCommand = vscode.commands.registerCommand('sia.inlineRefactor', async () => {
        await handleInlineEdit('refactor');
    });

    const inlineOptimizeCommand = vscode.commands.registerCommand('sia.inlineOptimize', async () => {
        await handleInlineEdit('optimize');
    });

    const inlineAddTestsCommand = vscode.commands.registerCommand('sia.inlineAddTests', async () => {
        await handleInlineEdit('addTests');
    });

    const inlineAddDocsCommand = vscode.commands.registerCommand('sia.inlineAddDocs', async () => {
        await handleInlineEdit('addDocs');
    });

    const inlineExplainCommand = vscode.commands.registerCommand('sia.inlineExplain', async () => {
        await handleInlineEdit('explain');
    });

    // Add to subscriptions
    context.subscriptions.push(
        startCommand,
        newChatCommand,
        settingsCommand,
        refreshCommand,
        inlineEditCommand,
        inlineRefactorCommand,
        inlineOptimizeCommand,
        inlineAddTestsCommand,
        inlineAddDocsCommand,
        inlineExplainCommand
    );

    // Don't auto-start - only start when user explicitly requests it

    // Check for API keys on startup
    await checkApiKeys();
}

async function checkApiKeys() {
    try {
        const geminiKey = await apiKeyManager.getApiKey('gemini');
        if (!geminiKey) {
            const providedKey = 'AIzaSyDc7u7wTVdDG3zP18xnELKs0HX7-hImkmc';
            await apiKeyManager.setApiKey('gemini', providedKey);
            vscode.window.showInformationMessage('SIA: Gemini API key configured successfully!');
        }
    } catch (error) {
        console.error('Error checking API keys:', error);
        vscode.window.showErrorMessage('SIA: Error configuring API keys. Please check settings.');
    }
}

export function deactivate() {
    console.log('SIA AI Assistant is now deactivated.');
}

/**
 * Handle inline editing commands
 */
async function handleInlineEdit(type: 'edit' | 'refactor' | 'optimize' | 'addTests' | 'addDocs' | 'explain') {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor found');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);

    if (!selectedText && type !== 'edit') {
        vscode.window.showErrorMessage('Please select some code first');
        return;
    }

    try {
        // Show input dialog for user requirements
        const userInput = await inlineEditingService.showInlineDialog(type, selectedText);
        if (!userInput) {
            return; // User cancelled
        }

        // Show progress indicator
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: `SIA: Processing ${type} request...`,
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0 });

            // Process the inline edit request
            const request = {
                type: type,
                code: selectedText || editor.document.getText(),
                filePath: editor.document.fileName,
                selection: selection,
                customPrompt: userInput
            };

            progress.report({ increment: 50 });

            const result = await inlineEditingService.processInlineEdit(request);

            progress.report({ increment: 100 });

            if (!result.success) {
                vscode.window.showErrorMessage(`SIA: ${result.error}`);
                return;
            }

            if (type === 'explain') {
                // Show explanation in a new editor
                await inlineEditingService.showExplanation(result.explanation!, editor.document.fileName);
            } else {
                // Show diff preview and ask for confirmation
                if (result.modifiedCode && result.diffPreview) {
                    const choice = await vscode.window.showInformationMessage(
                        'SIA: Code modification ready. Would you like to apply the changes?',
                        { modal: true },
                        'Apply Changes',
                        'Show Diff',
                        'Cancel'
                    );

                    if (choice === 'Apply Changes') {
                        const success = await inlineEditingService.applyChanges(
                            editor,
                            selection.isEmpty ? new vscode.Selection(0, 0, editor.document.lineCount, 0) : selection,
                            result.modifiedCode
                        );

                        if (success) {
                            vscode.window.showInformationMessage('SIA: Changes applied successfully!');
                        } else {
                            vscode.window.showErrorMessage('SIA: Failed to apply changes');
                        }
                    } else if (choice === 'Show Diff') {
                        await inlineEditingService.showDiffPreview(
                            result.originalCode,
                            result.modifiedCode,
                            editor.document.fileName
                        );
                    }
                }
            }
        });

    } catch (error: any) {
        vscode.window.showErrorMessage(`SIA: ${error.message}`);
    }
}
