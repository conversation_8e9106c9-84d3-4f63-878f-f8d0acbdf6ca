(()=>{var e={2980:(e,t,r)=>{"use strict";r(7360),r(5888),r(8412),r(3852),r(5584);var n=r(9892),o=r(5008),i=r(5088),s=r(224);function a(e,t){(t=t||{}).clipboard&&console.log("\n*** Clipboard option removed - use clipboardy or clipboard-cli directly ***\n");var r=Object.keys(e).length>0?e:i.defaults,s=Object.entries(r).reduce((function(e,r){var o=r[0],i=r[1],s=n[`get${o}`];return s?(i&&e.push(s(i,t)),e):e=e.concat((i||[]).map((function(e){var t=n[`get${e.replace(/\s/g,"")}Info`];return t?t():Promise.resolve(["Unknown"])})))}),[]);return Promise.all(s).then((function(e){var r=e.reduce((function(e,t){return t&&t[0]&&Object.assign(e,{[t[0]]:t}),e}),{});return function(e,t){var r=t.json?o.json:t.markdown?o.markdown:o.yaml;if(t.console){var n=!1;process.stdout.isTTY&&(n=!0),console.log(r(e,Object.assign({},t,{console:n})))}return r(e,Object.assign({},t,{console:!1}))}(Object.entries(i.defaults).reduce((function(e,t){var n=t[0],o=t[1];return r[n]?Object.assign(e,{[n]:r[n][1]}):Object.assign(e,{[n]:(o||[]).reduce((function(e,t){return r[t]?(r[t].shift(),1===r[t].length?Object.assign(e,{[t]:r[t][0]}):Object.assign(e,{[t]:{version:r[t][0],path:r[t][1]}})):e}),{})})}),{}),t)}))}e.exports={cli:function(e){if(e.all)return a(Object.assign({},i.defaults,{npmPackages:!0,npmGlobalPackages:!0}),e);if(e.raw)return a(JSON.parse(e.raw),e);if(e.helper){var t=n[`get${e.helper}`]||n[`get${e.helper}Info`]||n[e.helper];return t?t().then(console.log):console.error("Not Found")}var r=function(e,t){return e.toLowerCase().includes(t.toLowerCase())},o=Object.keys(e).filter((function(e){return Object.keys(i.defaults).some((function(t){return r(t,e)}))})),c=Object.entries(i.defaults).reduce((function(t,n){return o.some((function(e){return r(e,n[0])}))?Object.assign(t,{[n[0]]:n[1]||e[n[0]]}):t}),{});return e.preset?i[e.preset]?a(Object.assign({},s.omit(i[e.preset],["options"]),c),Object.assign({},i[e.preset].options,s.pick(e,["duplicates","fullTree","json","markdown","console"]))):console.error(`\nNo "${e.preset}" preset found.`):a(c,e)},helpers:n,main:a,run:function(e,t){return"string"==typeof e.preset?a(i[e.preset],t):a(e,t)}}},5008:(e,t,r)=>{"use strict";r(223),r(3852),r(4880),r(3708),r(5584),r(5308);var n=r(6232),o=r(224);function i(e,t){return o.log("trace","clean",e),Object.keys(e).reduce((function(r,n){return!t.showNotFound&&"Not Found"===e[n]||"N/A"===e[n]||void 0===e[n]||0===Object.keys(e[n]).length?r:o.isObject(e[n])?Object.values(e[n]).every((function(e){return"N/A"===e||!t.showNotFound&&"Not Found"===e}))?r:Object.assign(r,{[n]:i(e[n],t)}):Object.assign(r,{[n]:e[n]})}),{})}function s(e,t){o.log("trace","formatHeaders"),t||(t={type:"underline"});var r={underline:["[4m","[0m"]};return e.slice().split("\n").map((function(e){if(":"===e.slice("-1")){var n=e.match(/^[\s]*/g)[0];return`${n}${r[t.type][0]}${e.slice(n.length)}${r[t.type][1]}`}return e})).join("\n")}function a(e){return o.log("trace","formatPackages"),e.npmPackages?Object.assign(e,{npmPackages:Object.entries(e.npmPackages||{}).reduce((function(e,t){var r=t[0],n=t[1];if("Not Found"===n)return Object.assign(e,{[r]:n});var o=n.wanted?`${n.wanted} =>`:"",i=Array.isArray(n.installed)?n.installed.join(", "):n.installed,s=n.duplicates?`(${n.duplicates.join(", ")})`:"";return Object.assign(e,{[r]:`${o} ${i} ${s}`})}),{})}):e}function c(e,t,r){return r||(r={emptyMessage:"None"}),Array.isArray(t)&&(t=t.length>0?t.join(", "):r.emptyMessage),{[e]:t}}function u(e,t){return Object.entries(e).reduce((function(e,r){var n=r[0],i=r[1];return o.isObject(i)?Object.assign(e,{[n]:u(i,t)}):Object.assign(e,t(n,i))}),{})}function l(e){return o.log("trace","serializeArrays"),u(e,c)}function f(e){return o.log("trace","serializeVersionsAndPaths"),Object.entries(e).reduce((function(e,t){return Object.assign(e,{[t[0]]:Object.entries(t[1]).reduce((function(e,t){var r=t[0],n=t[1];return n.version?Object.assign(e,{[r]:[n.version,n.path].filter(Boolean).join(" - ")}):Object.assign(e,{[r]:[n][0]})}),{})},{})}),{})}function p(e){return n(e,{indent:"  ",prefix:"\n",postfix:"\n"})}function h(e){return e.slice().split("\n").map((function(e){if(""!==e){var t=":"===e.slice("-1"),r=e.search(/\S|$/);return t?`${"#".repeat(r/2+1)} `+e.slice(r):" - "+e.slice(r)}return""})).join("\n")}function d(e,t){return t||(t={indent:"  "}),JSON.stringify(e,null,t.indent)}e.exports={json:function(e,t){return o.log("trace","formatToJson"),t||(t={}),e=o.pipe([function(){return i(e,t)},t.title?function(e){return{[t.title]:e}}:o.noop,d])(e),e=t.console?`\n${e}\n`:e},markdown:function(e,t){return o.log("trace","formatToMarkdown"),o.pipe([function(){return i(e,t)},a,l,f,p,h,t.title?function(e){return`\n# ${t.title}${e}`}:o.noop])(e,t)},yaml:function(e,t){return o.log("trace","formatToYaml",t),o.pipe([function(){return i(e,t)},a,l,f,t.title?function(e){return{[t.title]:e}}:o.noop,p,t.console?s:o.noop])(e,t)}}},6732:(e,t,r)=>{"use strict";r(5888);var n=r(224);e.exports={getNodeInfo:function(){return n.log("trace","getNodeInfo"),Promise.all([n.isWindows?n.run("node -v").then(n.findVersion):n.which("node").then((function(e){return e?n.run(e+" -v"):Promise.resolve("")})).then(n.findVersion),n.which("node").then(n.condensePath)]).then((function(e){return n.determineFound("Node",e[0],e[1])}))},getnpmInfo:function(){return n.log("trace","getnpmInfo"),Promise.all([n.run("npm -v"),n.which("npm").then(n.condensePath)]).then((function(e){return n.determineFound("npm",e[0],e[1])}))},getWatchmanInfo:function(){return n.log("trace","getWatchmanInfo"),Promise.all([n.which("watchman").then((function(e){return e?n.run(e+" -v"):void 0})),n.which("watchman")]).then((function(e){return n.determineFound("Watchman",e[0],e[1])}))},getYarnInfo:function(){return n.log("trace","getYarnInfo"),Promise.all([n.run("yarn -v"),n.which("yarn").then(n.condensePath)]).then((function(e){return n.determineFound("Yarn",e[0],e[1])}))},getpnpmInfo:function(){return n.log("trace","getpnpmInfo"),Promise.all([n.run("pnpm -v"),n.which("pnpm").then(n.condensePath)]).then((function(e){return n.determineFound("pnpm",e[0],e[1])}))},getbunInfo:function(){return n.log("trace","getbunInfo"),Promise.all([n.run("bun -v"),n.which("bun").then(n.condensePath)]).then((function(e){return n.determineFound("bun",e[0],e[1])}))}}},2524:(e,t,r)=>{"use strict";r(3708),r(5888),r(8412);var n=r(2058),o=r(8558),i=r(224),s=r(7072);function a(e,t){var r;return(i.isLinux?i.run("firefox --version").then((function(e){return e.replace(/^.* ([^ ]*)/g,"$1")})):i.isMacOS&&"string"==typeof e&&e?i.getDarwinApplicationVersion(e):i.isWindows&&"string"==typeof t&&t?i.windowsExeExists(t).then((function(e){return r=e,e?i.run(`powershell ". '${e}' -v | Write-Output"`).then((function(e){return i.findVersion(e)})):i.NA})):Promise.resolve(i.NA)).then((function(e){return i.determineFound("Firefox",e,r||i.NA)}))}e.exports={getBraveBrowserInfo:function(){return i.log("trace","getBraveBrowser"),(i.isLinux?i.run("brave --version || brave-browser --version").then((function(e){return e.replace(/^.* ([^ ]*)/g,"$1")})):i.isMacOS?i.getDarwinApplicationVersion(i.browserBundleIdentifiers["Brave Browser"]).then(i.findVersion):Promise.resolve("N/A")).then((function(e){return i.determineFound("Brave Browser",e,"N/A")}))},getChromeInfo:function(){var e;if(i.log("trace","getChromeInfo"),i.isLinux)e=i.run("google-chrome --version").then((function(e){return e.replace(" dev","").replace(/^.* ([^ ]*)/g,"$1")}));else if(i.isMacOS)e=i.getDarwinApplicationVersion(i.browserBundleIdentifiers.Chrome).then(i.findVersion);else if(i.isWindows){var t;try{t=i.findVersion(n.readdirSync(s.join(process.env["ProgramFiles(x86)"],"Google/Chrome/Application")).join("\n"))}catch(e){t=i.NotFound}e=Promise.resolve(t)}else e=Promise.resolve("N/A");return e.then((function(e){return i.determineFound("Chrome",e,"N/A")}))},getChromeCanaryInfo:function(){return i.log("trace","getChromeCanaryInfo"),i.getDarwinApplicationVersion(i.browserBundleIdentifiers["Chrome Canary"]).then((function(e){return i.determineFound("Chrome Canary",e,"N/A")}))},getChromiumInfo:function(){return i.log("trace","getChromiumInfo"),(i.isLinux?i.run("chromium --version").then(i.findVersion):Promise.resolve("N/A")).then((function(e){return i.determineFound("Chromium",e,"N/A")}))},getEdgeInfo:function(){var e;if(i.log("trace","getEdgeInfo"),i.isWindows&&"10"===o.release().split(".")[0]){var t={Spartan:"Microsoft.MicrosoftEdge",Chromium:"Microsoft.MicrosoftEdge.Stable",ChromiumDev:"Microsoft.MicrosoftEdge.Dev"};e=Promise.all(Object.keys(t).map((function(e){return function(e,t){return i.run(`powershell get-appxpackage ${e}`).then((function(e){if(""!==i.findVersion(e))return`${t} (${i.findVersion(e)})`}))}(t[e],e)})).filter((function(e){return void 0!==e})))}else{if(!i.isMacOS)return Promise.resolve("N/A");e=i.getDarwinApplicationVersion(i.browserBundleIdentifiers["Microsoft Edge"])}return e.then((function(e){return i.determineFound("Edge",Array.isArray(e)?e.filter((function(e){return void 0!==e})):e,i.NA)}))},getFirefoxInfo:function(){i.log("trace","getFirefoxInfo"),a(i.browserBundleIdentifiers.Firefox,"Mozilla Firefox/firefox.exe")},getFirefoxDeveloperEditionInfo:function(){i.log("trace","getFirefoxDeveloperEditionInfo"),a(i.browserBundleIdentifiers["Firefox Developer Edition"],"Firefox Developer Edition/firefox.exe")},getFirefoxNightlyInfo:function(){return i.log("trace","getFirefoxNightlyInfo"),(i.isLinux?i.run("firefox-trunk --version").then((function(e){return e.replace(/^.* ([^ ]*)/g,"$1")})):i.isMacOS?i.getDarwinApplicationVersion(i.browserBundleIdentifiers["Firefox Nightly"]):Promise.resolve("N/A")).then((function(e){return i.determineFound("Firefox Nightly",e,"N/A")}))},getInternetExplorerInfo:function(){var e;if(i.log("trace","getInternetExplorerInfo"),i.isWindows){var t=[process.env.SYSTEMDRIVE||"C:","Program Files","Internet Explorer","iexplore.exe"].join("\\\\");e=i.run(`wmic datafile where "name='${t}'" get Version`).then(i.findVersion)}else e=Promise.resolve("N/A");return e.then((function(e){return i.determineFound("Internet Explorer",e,"N/A")}))},getSafariTechnologyPreviewInfo:function(){return i.log("trace","getSafariTechnologyPreviewInfo"),i.getDarwinApplicationVersion(i.browserBundleIdentifiers["Safari Technology Preview"]).then((function(e){return i.determineFound("Safari Technology Preview",e,"N/A")}))},getSafariInfo:function(){return i.log("trace","getSafariInfo"),i.getDarwinApplicationVersion(i.browserBundleIdentifiers.Safari).then((function(e){return i.determineFound("Safari",e,"N/A")}))}}},6620:(e,t,r)=>{"use strict";r(7360),r(5888);var n=r(224);e.exports={getMongoDBInfo:function(){return n.log("trace","getMongoDBInfo"),Promise.all([n.run("mongo --version").then(n.findVersion),n.which("mongo")]).then((function(e){return n.determineFound("MongoDB",e[0],e[1])}))},getMySQLInfo:function(){return n.log("trace","getMySQLInfo"),Promise.all([n.run("mysql --version").then((function(e){return`${n.findVersion(e,null,1)}${e.includes("MariaDB")?" (MariaDB)":""}`})),n.which("mysql")]).then((function(e){return n.determineFound("MySQL",e[0],e[1])}))},getPostgreSQLInfo:function(){return n.log("trace","getPostgreSQLInfo"),Promise.all([n.run("postgres --version").then(n.findVersion),n.which("postgres")]).then((function(e){return n.determineFound("PostgreSQL",e[0],e[1])}))},getSQLiteInfo:function(){return n.log("trace","getSQLiteInfo"),Promise.all([n.run("sqlite3 --version").then(n.findVersion),n.which("sqlite3")]).then((function(e){return n.determineFound("SQLite",e[0],e[1])}))}}},3224:(e,t,r)=>{"use strict";r(3708),r(8412),r(5888);var n=r(7072),o=r(224);e.exports={getAndroidStudioInfo:function(){var e=Promise.resolve("N/A");if(o.isMacOS){var t=[n.join("/","Applications","Android Studio.app","Contents","Info.plist"),n.join(process.env.HOME,"Applications","Android Studio.app","Contents","Info.plist"),n.join("/","Applications","JetBrains Toolbox","Android Studio.app","Contents","Info.plist"),n.join(process.env.HOME,"Applications","JetBrains Toolbox","Android Studio.app","Contents","Info.plist")];e=Promise.all(t.map((function(e){return o.fileExists(e).then((function(t){if(!t)return null;var r=o.generatePlistBuddyCommand(e.replace(/ /g,"\\ "),["CFBundleShortVersionString","CFBundleVersion"]);return o.run(r).then((function(e){return e.split("\n").join(" ")}))}))}))).then((function(e){return e.find((function(e){return null!==e}))||o.NotFound}))}else o.isLinux?e=Promise.all([o.run('cat /opt/android-studio/bin/studio.sh | grep "$Home/.AndroidStudio" | head -1').then(o.findVersion),o.run("cat /opt/android-studio/build.txt")]).then((function(e){return`${e[0]} ${e[1]}`.trim()||o.NotFound})):o.isWindows&&(e=Promise.all([o.run('wmic datafile where name="C:\\\\Program Files\\\\Android\\\\Android Studio\\\\bin\\\\studio.exe" get Version').then((function(e){return e.replace(/(\r\n|\n|\r)/gm,"")})),o.run('type "C:\\\\Program Files\\\\Android\\\\Android Studio\\\\build.txt"').then((function(e){return e.replace(/(\r\n|\n|\r)/gm,"")}))]).then((function(e){return`${e[0]} ${e[1]}`.trim()||o.NotFound})));return e.then((function(e){return o.determineFound("Android Studio",e)}))},getAtomInfo:function(){return o.log("trace","getAtomInfo"),Promise.all([o.getDarwinApplicationVersion(o.ideBundleIdentifiers.Atom),"N/A"]).then((function(e){return o.determineFound("Atom",e[0],e[1])}))},getEmacsInfo:function(){return o.log("trace","getEmacsInfo"),o.isMacOS||o.isLinux?Promise.all([o.run("emacs --version").then(o.findVersion),o.run("which emacs")]).then((function(e){return o.determineFound("Emacs",e[0],e[1])})):Promise.resolve(["Emacs","N/A"])},getIntelliJInfo:function(){return o.log("trace","getIntelliJInfo"),o.getDarwinApplicationVersion(o.ideBundleIdentifiers.IntelliJ).then((function(e){return o.determineFound("IntelliJ",e)}))},getNanoInfo:function(){return o.log("trace","getNanoInfo"),o.isLinux?Promise.all([o.run("nano --version").then(o.findVersion),o.run("which nano")]).then((function(e){return o.determineFound("Nano",e[0],e[1])})):Promise.resolve(["Nano","N/A"])},getNvimInfo:function(){return o.log("trace","getNvimInfo"),o.isMacOS||o.isLinux?Promise.all([o.run("nvim --version").then(o.findVersion),o.run("which nvim")]).then((function(e){return o.determineFound("Nvim",e[0],e[1])})):Promise.resolve(["Vim","N/A"])},getPhpStormInfo:function(){return o.log("trace","getPhpStormInfo"),o.getDarwinApplicationVersion(o.ideBundleIdentifiers.PhpStorm).then((function(e){return o.determineFound("PhpStorm",e)}))},getSublimeTextInfo:function(){return o.log("trace","getSublimeTextInfo"),Promise.all([o.run("subl --version").then((function(e){return o.findVersion(e,/\d+/)})),o.which("subl")]).then((function(e){return""===e[0]&&o.isMacOS?(o.log("trace","getSublimeTextInfo using plist"),Promise.all([o.getDarwinApplicationVersion(o.ideBundleIdentifiers["Sublime Text"]),"N/A"])):e})).then((function(e){return o.determineFound("Sublime Text",e[0],e[1])}))},getVimInfo:function(){return o.log("trace","getVimInfo"),o.isMacOS||o.isLinux?Promise.all([o.run("vim --version").then(o.findVersion),o.run("which vim")]).then((function(e){return o.determineFound("Vim",e[0],e[1])})):Promise.resolve(["Vim","N/A"])},getVSCodeInfo:function(){return o.log("trace","getVSCodeInfo"),Promise.all([o.run("code --version").then(o.findVersion),o.which("code")]).then((function(e){return o.determineFound("VSCode",e[0],e[1])}))},getVisualStudioInfo:function(){return o.log("trace","getVisualStudioInfo"),o.isWindows?o.run(`"${process.env["ProgramFiles(x86)"]}/Microsoft Visual Studio/Installer/vswhere.exe" -format json -prerelease`).then((function(e){var t=JSON.parse(e).map((function(e){return{Version:e.installationVersion,DisplayName:e.displayName}}));return o.determineFound("Visual Studio",t.map((function(e){return`${e.Version} (${e.DisplayName})`})))})).catch((function(){return Promise.resolve(["Visual Studio",o.NotFound])})):Promise.resolve(["Visual Studio",o.NA])},getWebStormInfo:function(){return o.log("trace","getWebStormInfo"),o.getDarwinApplicationVersion(o.ideBundleIdentifiers.WebStorm).then((function(e){return o.determineFound("WebStorm",e)}))},getXcodeInfo:function(){return o.log("trace","getXcodeInfo"),o.isMacOS?Promise.all([o.which("xcodebuild").then((function(e){return o.run(e+" -version")})).then((function(e){return`${o.findVersion(e)}/${e.split("Build version ")[1]}`})),o.which("xcodebuild")]).then((function(e){return o.determineFound("Xcode",e[0],e[1])})):Promise.resolve(["Xcode","N/A"])}}},9892:(e,t,r)=>{"use strict";function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r(5584);var o=r(6304),i=r(224),s=r(6732),a=r(2524),c=r(6620),u=r(3224),l=r(6436),f=r(760),p=r(2060),h=r(7508),d=r(9284),m=r(8144),g=r(5882),v=r(9676);e.exports=Object.assign({},i,o,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},o=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),o.forEach((function(t){n(e,t,r[t])}))}return e}({},s,a,c,u,l,f,p,h,d,m,g,v))},6436:(e,t,r)=>{"use strict";r(5888);var n=r(224);e.exports={getBashInfo:function(){return n.log("trace","getBashInfo"),Promise.all([n.run("bash --version").then(n.findVersion),n.which("bash")]).then((function(e){return n.determineFound("Bash",e[0],e[1])}))},getElixirInfo:function(){return n.log("trace","getElixirInfo"),Promise.all([n.run("elixir --version").then((function(e){return n.findVersion(e,/[Elixir]+\s([\d+.[\d+|.]+)/,1)})),n.which("elixir")]).then((function(e){return Promise.resolve(n.determineFound("Elixir",e[0],e[1]))}))},getErlangInfo:function(){return n.log("trace","getErlangInfo"),Promise.all([n.run("erl -eval \"{ok, Version} = file:read_file(filename:join([code:root_dir(), 'releases', erlang:system_info(otp_release), 'OTP_VERSION'])), io:fwrite(Version), halt().\" -noshell").then(n.findVersion),n.which("erl")]).then((function(e){return Promise.resolve(n.determineFound("Erlang",e[0],e[1]))}))},getGoInfo:function(){return n.log("trace","getGoInfo"),Promise.all([n.run("go version").then(n.findVersion),n.which("go")]).then((function(e){return n.determineFound("Go",e[0],e[1])}))},getJavaInfo:function(){return n.log("trace","getJavaInfo"),Promise.all([n.run("javac -version",{unify:!0}).then((function(e){return n.findVersion(e,/\d+\.[\w+|.|_|-]+/)})),n.run("which javac")]).then((function(e){return n.determineFound("Java",e[0],e[1])}))},getPerlInfo:function(){return n.log("trace","getPerlInfo"),Promise.all([n.run("perl -v").then(n.findVersion),n.which("perl")]).then((function(e){return n.determineFound("Perl",e[0],e[1])}))},getPHPInfo:function(){return n.log("trace","getPHPInfo"),Promise.all([n.run("php -v").then(n.findVersion),n.which("php")]).then((function(e){return n.determineFound("PHP",e[0],e[1])}))},getProtocInfo:function(){return n.log("trace","getProtocInfo"),Promise.all([n.run("protoc --version").then(n.findVersion),n.run("which protoc")]).then((function(e){return n.determineFound("Protoc",e[0],e[1])}))},getPythonInfo:function(){return n.log("trace","getPythonInfo"),Promise.all([n.run("python -V 2>&1").then(n.findVersion),n.run("which python")]).then((function(e){return n.determineFound("Python",e[0],e[1])}))},getPython3Info:function(){return n.log("trace","getPython3Info"),Promise.all([n.run("python3 -V 2>&1").then(n.findVersion),n.run("which python3")]).then((function(e){return n.determineFound("Python3",e[0],e[1])}))},getRInfo:function(){return n.log("trace","getRInfo"),Promise.all([n.run("R --version",{unify:!0}).then(n.findVersion),n.which("R")]).then((function(e){return n.determineFound("R",e[0],e[1])}))},getRubyInfo:function(){return n.log("trace","getRubyInfo"),Promise.all([n.run("ruby -v").then(n.findVersion),n.which("ruby")]).then((function(e){return n.determineFound("Ruby",e[0],e[1])}))},getRustInfo:function(){return n.log("trace","getRustInfo"),Promise.all([n.run("rustc --version").then(n.findVersion),n.run("which rustc")]).then((function(e){return n.determineFound("Rust",e[0],e[1])}))},getScalaInfo:function(){return n.log("trace","getScalaInfo"),n.isMacOS||n.isLinux?Promise.all([n.run("scalac -version").then(n.findVersion),n.run("which scalac")]).then((function(e){return n.determineFound("Scala",e[0],e[1])})):Promise.resolve(["Scala","N/A"])}}},760:(e,t,r)=>{"use strict";r(5888);var n=r(224);e.exports={getAptInfo:function(){return n.log("trace","getAptInfo"),n.isLinux?Promise.all([n.run("apt --version").then(n.findVersion),n.which("apt")]).then((function(e){return n.determineFound("Apt",e[0],e[1])})):Promise.all(["Apt","N/A"])},getCargoInfo:function(){return n.log("trace","getCargoInfo"),Promise.all([n.run("cargo --version").then(n.findVersion),n.which("cargo").then(n.condensePath)]).then((function(e){return n.determineFound("Cargo",e[0],e[1])}))},getCocoaPodsInfo:function(){return n.log("trace","getCocoaPodsInfo"),n.isMacOS?Promise.all([n.run("pod --version").then(n.findVersion),n.which("pod")]).then((function(e){return n.determineFound("CocoaPods",e[0],e[1])})):Promise.all(["CocoaPods","N/A"])},getComposerInfo:function(){return n.log("trace","getComposerInfo"),Promise.all([n.run("composer --version").then(n.findVersion),n.which("composer").then(n.condensePath)]).then((function(e){return n.determineFound("Composer",e[0],e[1])}))},getGradleInfo:function(){return n.log("trace","getGradleInfo"),Promise.all([n.run("gradle --version").then(n.findVersion),n.which("gradle").then(n.condensePath)]).then((function(e){return n.determineFound("Gradle",e[0],e[1])}))},getHomebrewInfo:function(){return n.log("trace","getHomebrewInfo"),n.isMacOS||n.isLinux?Promise.all([n.run("brew --version").then(n.findVersion),n.which("brew")]).then((function(e){return n.determineFound("Homebrew",e[0],e[1])})):Promise.all(["Homebrew","N/A"])},getMavenInfo:function(){return n.log("trace","getMavenInfo"),Promise.all([n.run("mvn --version").then(n.findVersion),n.which("mvn").then(n.condensePath)]).then((function(e){return n.determineFound("Maven",e[0],e[1])}))},getpip2Info:function(){return n.log("trace","getpip2Info"),Promise.all([n.run("pip2 --version").then(n.findVersion),n.which("pip2").then(n.condensePath)]).then((function(e){return n.determineFound("pip2",e[0],e[1])}))},getpip3Info:function(){return n.log("trace","getpip3Info"),Promise.all([n.run("pip3 --version").then(n.findVersion),n.which("pip3").then(n.condensePath)]).then((function(e){return n.determineFound("pip3",e[0],e[1])}))},getRubyGemsInfo:function(){return n.log("trace","getRubyGemsInfo"),Promise.all([n.run("gem --version").then(n.findVersion),n.which("gem")]).then((function(e){return n.determineFound("RubyGems",e[0],e[1])}))},getYumInfo:function(){return n.log("trace","getYumInfo"),n.isLinux?Promise.all([n.run("yum --version").then(n.findVersion),n.which("yum")]).then((function(e){return n.determineFound("Yum",e[0],e[1])})):Promise.all(["Yum","N/A"])}}},2060:(e,t,r)=>{"use strict";r(5888);var n=r(224),o=r(7072);e.exports={getYarnWorkspacesInfo:function(){return n.log("trace","getYarnWorkspacesInfo"),Promise.all([n.run("yarn -v"),n.getPackageJsonByPath("package.json").then((function(e){return e&&"workspaces"in e}))]).then((function(e){var t="Yarn Workspaces";return e[0]&&e[1]?Promise.resolve([t,e[0]]):Promise.resolve([t,"Not Found"])}))},getLernaInfo:function(){return n.log("trace","getLernaInfo"),Promise.all([n.getPackageJsonByName("lerna").then((function(e){return e&&e.version})),n.fileExists(o.join(process.cwd(),"lerna.json"))]).then((function(e){var t="Lerna";return e[0]&&e[1]?Promise.resolve([t,e[0]]):Promise.resolve([t,"Not Found"])}))}}},7508:(e,t,r)=>{"use strict";r(4880),r(5888),r(3708);var n=r(2058),o=r(7072),i=r(224);e.exports={getAndroidSDKInfo:function(){return i.run("sdkmanager --list").then((function(e){return!e&&process.env.ANDROID_HOME?i.run(`${process.env.ANDROID_HOME}/tools/bin/sdkmanager --list`):e})).then((function(e){return!e&&process.env.ANDROID_HOME?i.run(`${process.env.ANDROID_HOME}/cmdline-tools/latest/bin/sdkmanager --list`):e})).then((function(e){return!e&&i.isMacOS?i.run("~/Library/Android/sdk/tools/bin/sdkmanager --list"):e})).then((function(e){var t=i.parseSDKManagerOutput(e),r=function(e){var t,r=o.join(e,"source.properties");try{t=n.readFileSync(r,"utf8")}catch(e){if("ENOENT"===e.code)return;throw e}for(var i=t.split("\n"),s=0;s<i.length;s+=1){var a=i[s].split("=");if(2===a.length&&"Pkg.Revision"===a[0].trim())return a[1].trim()}},s=process.env.ANDROID_NDK?r(process.env.ANDROID_NDK):process.env.ANDROID_NDK_HOME?r(process.env.ANDROID_NDK_HOME):process.env.ANDROID_HOME?r(o.join(process.env.ANDROID_HOME,"ndk-bundle")):void 0;return t.buildTools.length||t.apiLevels.length||t.systemImages.length||s?Promise.resolve(["Android SDK",{"API Levels":t.apiLevels||i.NotFound,"Build Tools":t.buildTools||i.NotFound,"System Images":t.systemImages||i.NotFound,"Android NDK":s||i.NotFound}]):Promise.resolve(["Android SDK",i.NotFound])}))},getiOSSDKInfo:function(){return i.isMacOS?i.run("xcodebuild -showsdks").then((function(e){return e.match(/[\w]+\s[\d|.]+/g)})).then(i.uniq).then((function(e){return e.length?["iOS SDK",{Platforms:e}]:["iOS SDK",i.NotFound]})):Promise.resolve(["iOS SDK","N/A"])},getWindowsSDKInfo:function(){if(i.log("trace","getWindowsSDKInfo"),i.isWindows){var e=i.NotFound;return i.run("reg query HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\AppModelUnlock").then((function(t){e=t.split(/[\r\n]/g).slice(1).filter((function(e){return""!==e})).reduce((function(e,t){var r=t.match(/[^\s]+/g);return"0x0"!==r[2]&&"0x1"!==r[2]||(r[2]="0x1"===r[2]?"Enabled":"Disabled"),e[r[0]]=r[2],e}),{}),0===Object.keys(e).length&&(e=i.NotFound);try{var r=n.readdirSync(`${process.env["ProgramFiles(x86)"]}/Windows Kits/10/Platforms/UAP`);e.Versions=r}catch(e){}return Promise.resolve(["Windows SDK",e])}))}return Promise.resolve(["Windows SDK",i.NA])}}},9284:(e,t,r)=>{"use strict";r(5888);var n=r(224);e.exports={getApacheInfo:function(){return n.log("trace","getApacheInfo"),n.isMacOS||n.isLinux?Promise.all([n.run("apachectl -v").then(n.findVersion),n.run("which apachectl")]).then((function(e){return n.determineFound("Apache",e[0],e[1])})):Promise.resolve(["Apache","N/A"])},getNginxInfo:function(){return n.log("trace","getNginxInfo"),n.isMacOS||n.isLinux?Promise.all([n.run("nginx -v 2>&1").then(n.findVersion),n.run("which nginx")]).then((function(e){return n.determineFound("Nginx",e[0],e[1])})):Promise.resolve(["Nginx","N/A"])}}},8144:(e,t,r)=>{"use strict";r(3708),r(4880),r(5888);var n=r(2288),o=r(224),i=r(8558);e.exports={getContainerInfo:function(){return o.log("trace","getContainerInfo"),o.isLinux?Promise.all([o.fileExists("/.dockerenv"),o.readFile("/proc/self/cgroup")]).then((function(e){return o.log("trace","getContainerInfoThen",e),Promise.resolve(["Container",e[0]||e[1]?"Yes":"N/A"])})).catch((function(e){return o.log("trace","getContainerInfoCatch",e)})):Promise.resolve(["Container","N/A"])},getCPUInfo:function(){var e;o.log("trace","getCPUInfo");try{var t=i.cpus();e="("+t.length+") "+i.arch()+" "+t[0].model}catch(t){e="Unknown"}return Promise.all(["CPU",e])},getMemoryInfo:function(){return o.log("trace","getMemoryInfo"),Promise.all(["Memory",`${o.toReadableBytes(i.freemem())} / ${o.toReadableBytes(i.totalmem())}`])},getOSInfo:function(){var e,t;if(o.log("trace","getOSInfo"),o.isMacOS)e=o.run("sw_vers -productVersion ");else if(o.isLinux)e=o.run("cat /etc/os-release").then((function(e){var t=(e||"").match(/NAME="(.+)"/)||"",r=(e||"").match(/VERSION="(.+)"/)||["",""],n=null!==r?r[1]:"";return`${t[1]} ${n}`.trim()||""}));else if(o.isWindows){e=Promise.resolve(i.release());var r=i.release().split(".");"10"===r[0]&&"0"===r[1]&&r[2]>=22e3&&(t="Windows 11")}else e=Promise.resolve();return e.then((function(e){return t=t||n(i.platform(),i.release()),e&&(t+=` ${e}`),["OS",t]}))},getShellInfo:function(){if(o.log("trace","getShellInfo",process.env),o.isMacOS||o.isLinux){var e=process.env.SHELL||o.runSync("getent passwd $LOGNAME | cut -d: -f7 | head -1"),t=`${e} --version 2>&1`;return e.match("/bin/ash")&&(t=`${e} --help 2>&1`),Promise.all([o.run(t).then(o.findVersion),o.which(e)]).then((function(e){return o.determineFound("Shell",e[0]||"Unknown",e[1])}))}return Promise.resolve(["Shell","N/A"])},getGLibcInfo:function(){return o.log("trace","getGLibc"),o.isLinux?Promise.all([o.run("ldd --version").then(o.findVersion)]).then((function(e){return o.determineFound("GLibc",e[0]||"Unknown")})):Promise.resolve(["GLibc","N/A"])}}},5882:(e,t,r)=>{"use strict";r(5888);var n=r(224);e.exports={getBazelInfo:function(){return n.log("trace","getBazelInfo"),Promise.all([n.run("bazel --version").then(n.findVersion),n.run("which bazel")]).then((function(e){return n.determineFound("Bazel",e[0],e[1])}))},getCMakeInfo:function(){return n.log("trace","getCMakeInfo"),Promise.all([n.run("cmake --version").then(n.findVersion),n.run("which cmake")]).then((function(e){return n.determineFound("CMake",e[0],e[1])}))},getGCCInfo:function(){return n.log("trace","getGCCInfo"),n.isMacOS||n.isLinux?Promise.all([n.run("gcc -v 2>&1").then(n.findVersion),n.run("which gcc")]).then((function(e){return n.determineFound("GCC",e[0],e[1])})):Promise.resolve(["GCC","N/A"])},getClangInfo:function(){return n.log("trace","getClangInfo"),Promise.all([n.run("clang --version").then(n.findVersion),n.which("clang")]).then((function(e){return n.determineFound("Clang",e[0],e[1])}))},getGitInfo:function(){return n.log("trace","getGitInfo"),Promise.all([n.run("git --version").then(n.findVersion),n.run("which git")]).then((function(e){return n.determineFound("Git",e[0],e[1])}))},getMakeInfo:function(){return n.log("trace","getMakeInfo"),n.isMacOS||n.isLinux?Promise.all([n.run("make --version").then(n.findVersion),n.run("which make")]).then((function(e){return n.determineFound("Make",e[0],e[1])})):Promise.resolve(["Make","N/A"])},getNinjaInfo:function(){return n.log("trace","getNinjaInfo"),Promise.all([n.run("ninja --version").then(n.findVersion),n.run("which ninja")]).then((function(e){return n.determineFound("Ninja",e[0],e[1])}))},getMercurialInfo:function(){return n.log("trace","getMercurialInfo"),n.isMacOS||n.isLinux?Promise.all([n.run("hg --version").then(n.findVersion),n.run("which hg")]).then((function(e){return n.determineFound("Mercurial",e[0],e[1])})):Promise.resolve(["Mercurial","N/A"])},getSubversionInfo:function(){return n.log("trace","getSubversionInfo"),n.isMacOS||n.isLinux?Promise.all([n.run("svn --version").then(n.findVersion),n.run("which svn")]).then((function(e){return n.determineFound("Subversion",e[0],e[1])})):Promise.resolve(["Subversion","N/A"])},getFFmpegInfo:function(){return n.log("trace","getFFmpegInfo"),Promise.all([n.run("ffmpeg -version").then(n.findVersion),n.which("ffmpeg")]).then((function(e){return n.determineFound("FFmpeg",e[0],e[1])}))},getCurlInfo:function(){return n.log("trace","getCurlInfo"),Promise.all([n.run("curl --version").then(n.findVersion),n.which("curl")]).then((function(e){return n.determineFound("Curl",e[0],e[1])}))},getOpenSSLInfo:function(){return n.log("trace","getOpenSSLInfo"),Promise.all([n.run("openssl version").then(n.findVersion),n.which("openssl")]).then((function(e){return n.determineFound("OpenSSL",e[0],e[1])}))}}},9676:(e,t,r)=>{"use strict";r(5888);var n=r(224);e.exports={getDockerInfo:function(){return n.log("trace","getDockerInfo"),Promise.all([n.run("docker --version").then(n.findVersion),n.which("docker")]).then((function(e){return n.determineFound("Docker",e[0],e[1])}))},getDockerComposeInfo:function(){return n.log("trace","getDockerComposeInfo"),Promise.all([n.run("docker-compose --version").then(n.findVersion),n.which("docker-compose")]).then((function(e){return n.determineFound("Docker Compose",e[0],e[1])}))},getParallelsInfo:function(){return n.log("trace","getParallelsInfo"),Promise.all([n.run("prlctl --version").then(n.findVersion),n.which("prlctl")]).then((function(e){return n.determineFound("Parallels",e[0],e[1])}))},getPodmanInfo:function(){return n.log("trace","getPodmanInfo"),Promise.all([n.run("podman --version").then(n.findVersion),n.which("podman")]).then((function(e){return n.determineFound("Podman",e[0],e[1])}))},getVirtualBoxInfo:function(){return n.log("trace","getVirtualBoxInfo"),Promise.all([n.run("vboxmanage --version").then(n.findVersion),n.which("vboxmanage")]).then((function(e){return n.determineFound("VirtualBox",e[0],e[1])}))},getVMwareFusionInfo:function(){return n.log("trace","getVMwareFusionInfo"),n.getDarwinApplicationVersion("com.vmware.fusion").then((function(e){return n.determineFound("VMWare Fusion",e,"N/A")}))}}},6032:e=>{"use strict";e.exports={androidSystemImages:/system-images;([\S \t]+)/g,androidAPILevels:/platforms;android-(\d+)[\S\s]/g,androidBuildTools:/build-tools;([\d|.]+)[\S\s]/g}},6304:(e,t,r)=>{"use strict";r(4880),r(5584),r(5888),r(7360),r(3708);var n=r(808),o=r(7072),i=r(224),s=function(e){var t=e.split("node_modules"+o.sep),r=t[t.length-1];return"@"===r.charAt(0)?[r.split(o.sep)[0],r.split(o.sep)[1]].join("/"):r.split(o.sep)[0]};e.exports={getnpmPackages:function(e,t){i.log("trace","getnpmPackages"),t||(t={});var r=null,n=null;return"string"==typeof e&&(e.includes("*")||e.includes("?")||e.includes("+")||e.includes("!")?r=e:e=e.split(",")),Promise.all(["npmPackages",i.getPackageJsonByPath("package.json").then((function(e){return Object.assign({},(e||{}).devDependencies||{},(e||{}).dependencies||{})})).then((function(e){return n=e,t.fullTree||t.duplicates||r?i.getAllPackageJsonPaths(r):Promise.resolve(Object.keys(e||[]).map((function(e){return o.join("node_modules",e,"package.json")})))})).then((function(o){return!r&&"boolean"!=typeof e||t.fullTree?Array.isArray(e)?Promise.resolve((o||[]).filter((function(t){return e.includes(s(t))}))):Promise.resolve(o):Promise.resolve((o||[]).filter((function(e){return Object.keys(n||[]).includes(s(e))})))})).then((function(e){return Promise.all([e,Promise.all(e.map((function(e){return i.getPackageJsonByPath(e)})))])})).then((function(e){var r=e[0],o=e[1].reduce((function(e,n,o){return n&&n.name?(e[n.name]||(e[n.name]={}),t.duplicates&&(e[n.name].duplicates=i.uniq((e[n.name].duplicates||[]).concat(n.version))),1===(r[o].match(/node_modules/g)||[]).length&&(e[n.name].installed=n.version),e):e}),{});return Object.keys(o).forEach((function(e){o[e].duplicates&&o[e].installed&&(o[e].duplicates=o[e].duplicates.filter((function(t){return t!==o[e].installed}))),n[e]&&(o[e].wanted=n[e])})),o})).then((function(r){return t.showNotFound&&Array.isArray(e)&&e.forEach((function(e){r[e]||(r[e]="Not Found")})),r})).then((function(e){return i.sortObject(e)}))])},getnpmGlobalPackages:function(e,t){i.log("trace","getnpmGlobalPackages",e);var r=null;return"string"==typeof e?e.includes("*")||e.includes("?")||e.includes("+")||e.includes("!")?r=e:e=e.split(","):Array.isArray(e)||(e=!0),Promise.all(["npmGlobalPackages",i.run("npm get prefix --global").then((function(e){return new Promise((function(t,s){return n(o.join(e,i.isWindows?"":"lib","node_modules",r||"{*,@*/*}","package.json"),(function(e,r){e||t(r),s(e)}))}))})).then((function(t){return Promise.all(t.filter((function(t){return"boolean"==typeof e||null!==r||e.includes(s(t))})).map((function(e){return i.getPackageJsonByFullPath(e)})))})).then((function(e){return e.reduce((function(e,t){return t?Object.assign(e,{[t.name]:t.version}):e}),{})})).then((function(r){return t.showNotFound&&Array.isArray(e)&&e.forEach((function(e){r[e]||(r[e]="Not Found")})),r}))])}}},5088:e=>{"use strict";e.exports={defaults:{System:["OS","CPU","Memory","Container","Shell"],Binaries:["Node","Yarn","npm","pnpm","bun","Watchman"],Managers:["Apt","Cargo","CocoaPods","Composer","Gradle","Homebrew","Maven","pip2","pip3","RubyGems","Yum"],Utilities:["Bazel","CMake","Make","GCC","Git","Clang","Ninja","Mercurial","Subversion","FFmpeg","Curl","OpenSSL"],Servers:["Apache","Nginx"],Virtualization:["Docker","Docker Compose","Parallels","VirtualBox","VMware Fusion"],SDKs:["iOS SDK","Android SDK","Windows SDK"],IDEs:["Android Studio","Atom","Emacs","IntelliJ","NVim","Nano","PhpStorm","Sublime Text","VSCode","Visual Studio","Vim","WebStorm","Xcode"],Languages:["Bash","Go","Elixir","Erlang","Java","Perl","PHP","Protoc","Python","Python3","R","Ruby","Rust","Scala"],Databases:["MongoDB","MySQL","PostgreSQL","SQLite"],Browsers:["Brave Browser","Chrome","Chrome Canary","Chromium","Edge","Firefox","Firefox Developer Edition","Firefox Nightly","Internet Explorer","Safari","Safari Technology Preview"],Monorepos:["Yarn Workspaces","Lerna"],npmPackages:null,npmGlobalPackages:null},cssnano:{System:["OS","CPU"],Binaries:["Node","Yarn","npm","pnpm","bun"],npmPackages:["cssnano","postcss"],options:{duplicates:!0}},jest:{System:["OS","CPU"],Binaries:["Node","Yarn","npm","pnpm","bun"],npmPackages:["jest"]},"react-native":{System:["OS","CPU"],Binaries:["Node","Yarn","npm","pnpm","bun","Watchman"],SDKs:["iOS SDK","Android SDK","Windows SDK"],IDEs:["Android Studio","Xcode","Visual Studio"],npmPackages:["react","react-native"],npmGlobalPackages:["react-native-cli"]},nyc:{System:["OS","CPU","Memory"],Binaries:["Node","Yarn","npm","pnpm","bun"],npmPackages:"/**/{*babel*,@babel/*/,*istanbul*,nyc,source-map-support,typescript,ts-node}"},webpack:{System:["OS","CPU"],Binaries:["Node","Yarn","npm","pnpm","bun"],npmPackages:"*webpack*",npmGlobalPackages:["webpack","webpack-cli"]},"styled-components":{System:["OS","CPU"],Binaries:["Node","Yarn","npm","pnpm","bun"],Browsers:["Chrome","Firefox","Safari"],npmPackages:"*styled-components*"},"create-react-app":{System:["OS","CPU"],Binaries:["Node","npm","Yarn","pnpm","bun"],Browsers:["Chrome","Edge","Internet Explorer","Firefox","Safari"],npmPackages:["react","react-dom","react-scripts"],npmGlobalPackages:["create-react-app"],options:{duplicates:!0,showNotFound:!0}},apollo:{System:["OS"],Binaries:["Node","npm","Yarn","pnpm","bun"],Browsers:["Chrome","Edge","Firefox","Safari"],npmPackages:"{*apollo*,@apollo/*}",npmGlobalPackages:"{*apollo*,@apollo/*}"},"react-native-web":{System:["OS","CPU"],Binaries:["Node","npm","Yarn","pnpm","bun"],Browsers:["Chrome","Edge","Internet Explorer","Firefox","Safari"],npmPackages:["react","react-native-web"],options:{showNotFound:!0}},babel:{System:["OS"],Binaries:["Node","npm","Yarn","pnpm","bun"],Monorepos:["Yarn Workspaces","Lerna"],npmPackages:"{*babel*,@babel/*,eslint,webpack,create-react-app,react-native,lerna,jest,next,rollup}"},playwright:{System:["OS","CPU","Memory","Container"],Binaries:["Node","Yarn","npm","pnpm","bun"],Languages:["Bash"],IDEs:["VSCode"],npmPackages:"{playwright*,@playwright/*}"}}},224:(e,t,r)=>{"use strict";r(4880),r(4260),r(5584),r(7276),r(4228),r(3708),r(8412),r(5308),r(6568),r(5888);var n=r(7072),o=r(2058),i=r(8558),s=r(4368),a=r(8460),c=r(808),u=r(6032),l=function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).unify,r=void 0!==t&&t;return new Promise((function(t){s.exec(e,{stdio:[0,"pipe","ignore"]},(function(e,n,o){var i;i=r?n.toString()+o.toString():n.toString(),t((e?"":i).trim())}))}))},f=function(e){var t=Object.values(Array.prototype.slice.call(arguments).slice(1));(process.env.ENVINFO_DEBUG||"").toLowerCase()===e&&console.log(e,JSON.stringify(t))},p=function(e){return new Promise((function(t){o.readFile(e,"utf8",(function(e,r){return t(r||null)}))}))},h=function(e){return p(e).then((function(e){return e?JSON.parse(e):null}))},d=/\d+\.[\d+|.]+/g,m=function(e){f("trace","findDarwinApplication",e);var t=`mdfind "kMDItemCFBundleIdentifier=='${e}'"`;return f("trace",t),l(t).then((function(e){return e.replace(/(\s)/g,"\\ ")}))},g=function(e,t){var r=(t||["CFBundleShortVersionString"]).map((function(e){return"-c Print:"+e}));return["/usr/libexec/PlistBuddy"].concat(r).concat([e]).join(" ")},v=function(e,t){for(var r=[],n=null;null!==(n=e.exec(t));)r.push(n);return r};e.exports={run:l,log:f,fileExists:function(e){return new Promise((function(t){o.stat(e,(function(r){return t(r?null:e)}))}))},windowsExeExists:function(e){return new Promise((function(t){var r;o.access(r=n.join(process.env.ProgramFiles,`${e}`),o.constants.R_OK,(function(i){i?o.access(r=n.join(process.env["ProgramFiles(x86)"],`${e}`),o.constants.X_OK,(function(e){t(e?null:r)})):t(r)}))}))},readFile:p,requireJson:h,versionRegex:d,findDarwinApplication:m,generatePlistBuddyCommand:g,matchAll:v,parseSDKManagerOutput:function(e){var t=e.split("Available")[0];return{apiLevels:v(u.androidAPILevels,t).map((function(e){return e[1]})),buildTools:v(u.androidBuildTools,t).map((function(e){return e[1]})),systemImages:v(u.androidSystemImages,t).map((function(e){return e[1].split("|").map((function(e){return e.trim()}))})).map((function(e){return e[0].split(";")[0]+" | "+e[2].split(" System Image")[0]}))}},isLinux:"linux"===process.platform,isMacOS:"darwin"===process.platform,NA:"N/A",NotFound:"Not Found",isWindows:process.platform.startsWith("win"),isObject:function(e){return"object"==typeof e&&!Array.isArray(e)},noop:function(e){return e},pipe:function(e){return function(t){return e.reduce((function(e,t){return t(e)}),t)}},browserBundleIdentifiers:{"Brave Browser":"com.brave.Browser",Chrome:"com.google.Chrome","Chrome Canary":"com.google.Chrome.canary",Firefox:"org.mozilla.firefox","Firefox Developer Edition":"org.mozilla.firefoxdeveloperedition","Firefox Nightly":"org.mozilla.nightly","Microsoft Edge":"com.microsoft.edgemac",Safari:"com.apple.Safari","Safari Technology Preview":"com.apple.SafariTechnologyPreview"},ideBundleIdentifiers:{Atom:"com.github.atom",IntelliJ:"com.jetbrains.intellij",PhpStorm:"com.jetbrains.PhpStorm","Sublime Text":"com.sublimetext.3",WebStorm:"com.jetbrains.WebStorm"},runSync:function(e){return(s.execSync(e,{stdio:[0,"pipe","ignore"]}).toString()||"").trim()},which:function(e){return new Promise((function(t){return a(e,(function(e,r){return t(r)}))}))},getDarwinApplicationVersion:function(e){var t;return f("trace","getDarwinApplicationVersion",e),t="darwin"!==process.platform?"N/A":m(e).then((function(e){return l(g(n.join(e,"Contents","Info.plist"),["CFBundleShortVersionString"]))})),Promise.resolve(t)},uniq:function(e){return Array.from(new Set(e))},toReadableBytes:function(e){var t=Math.floor(Math.log(e)/Math.log(1024));return e?(e/Math.pow(1024,t)).toFixed(2)+" "+["B","KB","MB","GB","TB","PB"][t]:"0 Bytes"},omit:function(e,t){return Object.keys(e).filter((function(e){return t.indexOf(e)<0})).reduce((function(t,r){return Object.assign(t,{[r]:e[r]})}),{})},pick:function(e,t){return Object.keys(e).filter((function(e){return t.indexOf(e)>=0})).reduce((function(t,r){return Object.assign(t,{[r]:e[r]})}),{})},getPackageJsonByName:function(e){return h(n.join(process.cwd(),"node_modules",e,"package.json"))},getPackageJsonByPath:function(e){return h(n.join(process.cwd(),e))},getPackageJsonByFullPath:function(e){return f("trace","getPackageJsonByFullPath",e),h(e)},getAllPackageJsonPaths:function(e){return f("trace","getAllPackageJsonPaths",e),new Promise((function(t){return c(e?n.join("node_modules",e,"package.json"):n.join("node_modules","**","package.json"),(function(e,r){return t(r.map(n.normalize)||[])}))}))},sortObject:function(e){return Object.keys(e).sort().reduce((function(t,r){return t[r]=e[r],t}),{})},findVersion:function(e,t,r){f("trace","findVersion",e,t,r);var n=r||0,o=t||d,i=e.match(o);return i?i[n]:e},condensePath:function(e){return(e||"").replace(i.homedir(),"~")},determineFound:function(e,t,r){return f("trace","determineFound",e,t,r),"N/A"===t?Promise.resolve([e,"N/A"]):t&&0!==Object.keys(t).length?r?Promise.resolve([e,t,r]):Promise.resolve([e,t]):Promise.resolve([e,"Not Found"])}}},9016:e=>{"use strict";function t(e,t,o){e instanceof RegExp&&(e=r(e,o)),t instanceof RegExp&&(t=r(t,o));var i=n(e,t,o);return i&&{start:i[0],end:i[1],pre:o.slice(0,i[0]),body:o.slice(i[0]+e.length,i[1]),post:o.slice(i[1]+t.length)}}function r(e,t){var r=t.match(e);return r?r[0]:null}function n(e,t,r){var n,o,i,s,a,c=r.indexOf(e),u=r.indexOf(t,c+1),l=c;if(c>=0&&u>0){for(n=[],i=r.length;l>=0&&!a;)l==c?(n.push(l),c=r.indexOf(e,l+1)):1==n.length?a=[n.pop(),u]:((o=n.pop())<i&&(i=o,s=u),u=r.indexOf(t,l+1)),l=c<u&&c>=0?c:u;n.length&&(a=[i,s])}return a}e.exports=t,t.range=n},1620:(e,t,r)=>{var n=r(6596),o=r(9016);e.exports=function(e){return e?("{}"===e.substr(0,2)&&(e="\\{\\}"+e.substr(2)),v(function(e){return e.split("\\\\").join(i).split("\\{").join(s).split("\\}").join(a).split("\\,").join(c).split("\\.").join(u)}(e),!0).map(f)):[]};var i="\0SLASH"+Math.random()+"\0",s="\0OPEN"+Math.random()+"\0",a="\0CLOSE"+Math.random()+"\0",c="\0COMMA"+Math.random()+"\0",u="\0PERIOD"+Math.random()+"\0";function l(e){return parseInt(e,10)==e?parseInt(e,10):e.charCodeAt(0)}function f(e){return e.split(i).join("\\").split(s).join("{").split(a).join("}").split(c).join(",").split(u).join(".")}function p(e){if(!e)return[""];var t=[],r=o("{","}",e);if(!r)return e.split(",");var n=r.pre,i=r.body,s=r.post,a=n.split(",");a[a.length-1]+="{"+i+"}";var c=p(s);return s.length&&(a[a.length-1]+=c.shift(),a.push.apply(a,c)),t.push.apply(t,a),t}function h(e){return"{"+e+"}"}function d(e){return/^-?0\d/.test(e)}function m(e,t){return e<=t}function g(e,t){return e>=t}function v(e,t){var r=[],i=o("{","}",e);if(!i||/\$$/.test(i.pre))return[e];var s,c=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(i.body),u=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(i.body),f=c||u,y=i.body.indexOf(",")>=0;if(!f&&!y)return i.post.match(/,.*\}/)?v(e=i.pre+"{"+i.body+a+i.post):[e];if(f)s=i.body.split(/\.\./);else if(1===(s=p(i.body)).length&&1===(s=v(s[0],!1).map(h)).length)return(x=i.post.length?v(i.post,!1):[""]).map((function(e){return i.pre+s[0]+e}));var b,w=i.pre,x=i.post.length?v(i.post,!1):[""];if(f){var S=l(s[0]),P=l(s[1]),O=Math.max(s[0].length,s[1].length),I=3==s.length?Math.abs(l(s[2])):1,E=m;P<S&&(I*=-1,E=g);var j=s.some(d);b=[];for(var _=S;E(_,P);_+=I){var A;if(u)"\\"===(A=String.fromCharCode(_))&&(A="");else if(A=String(_),j){var k=O-A.length;if(k>0){var N=new Array(k+1).join("0");A=_<0?"-"+N+A.slice(1):N+A}}b.push(A)}}else b=n(s,(function(e){return v(e,!1)}));for(var F=0;F<b.length;F++)for(var C=0;C<x.length;C++){var M=w+b[F]+x[C];(!t||f||M)&&r.push(M)}return r}},6596:e=>{e.exports=function(e,r){for(var n=[],o=0;o<e.length;o++){var i=r(e[o],o);t(i)?n.push.apply(n,i):n.push(i)}return n};var t=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},2016:e=>{e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},7384:(e,t,r)=>{var n=r(7096)("unscopables"),o=Array.prototype;null==o[n]&&r(2336)(o,n,{}),e.exports=function(e){o[n][e]=!0}},2388:e=>{e.exports=function(e,t,r,n){if(!(e instanceof t)||void 0!==n&&n in e)throw TypeError(r+": incorrect invocation!");return e}},3504:(e,t,r)=>{var n=r(3888);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},7504:(e,t,r)=>{var n=r(2780),o=r(3528),i=r(8508);e.exports=function(e){return function(t,r,s){var a,c=n(t),u=o(c.length),l=i(s,u);if(e&&r!=r){for(;u>l;)if((a=c[l++])!=a)return!0}else for(;u>l;l++)if((e||l in c)&&c[l]===r)return e||l||0;return!e&&-1}}},5848:(e,t,r)=>{var n=r(6924),o=r(7096)("toStringTag"),i="Arguments"==n(function(){return arguments}());e.exports=function(e){var t,r,s;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),o))?r:i?n(t):"Object"==(s=n(t))&&"function"==typeof t.callee?"Arguments":s}},6924:e=>{var t={}.toString;e.exports=function(e){return t.call(e).slice(8,-1)}},148:(e,t,r)=>{"use strict";var n=r(8520).f,o=r(3472),i=r(7704),s=r(800),a=r(2388),c=r(7228),u=r(6952),l=r(172),f=r(3384),p=r(1668),h=r(2020).fastKey,d=r(2772),m=p?"_s":"size",g=function(e,t){var r,n=h(t);if("F"!==n)return e._i[n];for(r=e._f;r;r=r.n)if(r.k==t)return r};e.exports={getConstructor:function(e,t,r,u){var l=e((function(e,n){a(e,l,t,"_i"),e._t=t,e._i=o(null),e._f=void 0,e._l=void 0,e[m]=0,null!=n&&c(n,r,e[u],e)}));return i(l.prototype,{clear:function(){for(var e=d(this,t),r=e._i,n=e._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete r[n.i];e._f=e._l=void 0,e[m]=0},delete:function(e){var r=d(this,t),n=g(r,e);if(n){var o=n.n,i=n.p;delete r._i[n.i],n.r=!0,i&&(i.n=o),o&&(o.p=i),r._f==n&&(r._f=o),r._l==n&&(r._l=i),r[m]--}return!!n},forEach:function(e){d(this,t);for(var r,n=s(e,arguments.length>1?arguments[1]:void 0,3);r=r?r.n:this._f;)for(n(r.v,r.k,this);r&&r.r;)r=r.p},has:function(e){return!!g(d(this,t),e)}}),p&&n(l.prototype,"size",{get:function(){return d(this,t)[m]}}),l},def:function(e,t,r){var n,o,i=g(e,t);return i?i.v=r:(e._l=i={i:o=h(t,!0),k:t,v:r,p:n=e._l,n:void 0,r:!1},e._f||(e._f=i),n&&(n.n=i),e[m]++,"F"!==o&&(e._i[o]=i)),e},getEntry:g,setStrong:function(e,t,r){u(e,t,(function(e,r){this._t=d(e,t),this._k=r,this._l=void 0}),(function(){for(var e=this,t=e._k,r=e._l;r&&r.r;)r=r.p;return e._t&&(e._l=r=r?r.n:e._t._f)?l(0,"keys"==t?r.k:"values"==t?r.v:[r.k,r.v]):(e._t=void 0,l(1))}),r?"entries":"values",!r,!0),f(t)}}},9412:(e,t,r)=>{"use strict";var n=r(2804),o=r(7076),i=r(8868),s=r(7704),a=r(2020),c=r(7228),u=r(2388),l=r(3888),f=r(9316),p=r(8380),h=r(6256),d=r(2672);e.exports=function(e,t,r,m,g,v){var y=n[e],b=y,w=g?"set":"add",x=b&&b.prototype,S={},P=function(e){var t=x[e];i(x,e,"delete"==e||"has"==e?function(e){return!(v&&!l(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return v&&!l(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,r){return t.call(this,0===e?0:e,r),this})};if("function"==typeof b&&(v||x.forEach&&!f((function(){(new b).entries().next()})))){var O=new b,I=O[w](v?{}:-0,1)!=O,E=f((function(){O.has(1)})),j=p((function(e){new b(e)})),_=!v&&f((function(){for(var e=new b,t=5;t--;)e[w](t,t);return!e.has(-0)}));j||((b=t((function(t,r){u(t,b,e);var n=d(new y,t,b);return null!=r&&c(r,g,n[w],n),n}))).prototype=x,x.constructor=b),(E||_)&&(P("delete"),P("has"),g&&P("get")),(_||I)&&P(w),v&&x.clear&&delete x.clear}else b=m.getConstructor(t,e,g,w),s(b.prototype,r),a.NEED=!0;return h(b,e),S[e]=b,o(o.G+o.W+o.F*(b!=y),S),v||m.setStrong(b,e,g),b}},8432:e=>{var t=e.exports={version:"2.5.7"};"number"==typeof __e&&(__e=t)},2340:(e,t,r)=>{"use strict";var n=r(8520),o=r(8164);e.exports=function(e,t,r){t in e?n.f(e,t,o(0,r)):e[t]=r}},800:(e,t,r)=>{var n=r(2016);e.exports=function(e,t,r){if(n(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,o){return e.call(t,r,n,o)}}return function(){return e.apply(t,arguments)}}},24:e=>{e.exports=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}},1668:(e,t,r)=>{e.exports=!r(9316)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},1300:(e,t,r)=>{var n=r(3888),o=r(2804).document,i=n(o)&&n(o.createElement);e.exports=function(e){return i?o.createElement(e):{}}},444:e=>{e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},7076:(e,t,r)=>{var n=r(2804),o=r(8432),i=r(2336),s=r(8868),a=r(800),c="prototype",u=function(e,t,r){var l,f,p,h,d=e&u.F,m=e&u.G,g=e&u.S,v=e&u.P,y=e&u.B,b=m?n:g?n[t]||(n[t]={}):(n[t]||{})[c],w=m?o:o[t]||(o[t]={}),x=w[c]||(w[c]={});for(l in m&&(r=t),r)p=((f=!d&&b&&void 0!==b[l])?b:r)[l],h=y&&f?a(p,n):v&&"function"==typeof p?a(Function.call,p):p,b&&s(b,l,p,e&u.U),w[l]!=p&&i(w,l,h),v&&x[l]!=p&&(x[l]=p)};n.core=o,u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},9316:e=>{e.exports=function(e){try{return!!e()}catch(e){return!0}}},4448:(e,t,r)=>{"use strict";var n=r(2336),o=r(8868),i=r(9316),s=r(24),a=r(7096);e.exports=function(e,t,r){var c=a(e),u=r(s,c,""[e]),l=u[0],f=u[1];i((function(){var t={};return t[c]=function(){return 7},7!=""[e](t)}))&&(o(String.prototype,e,l),n(RegExp.prototype,c,2==t?function(e,t){return f.call(e,this,t)}:function(e){return f.call(e,this)}))}},8068:(e,t,r)=>{"use strict";var n=r(3504);e.exports=function(){var e=n(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},7228:(e,t,r)=>{var n=r(800),o=r(2372),i=r(3164),s=r(3504),a=r(3528),c=r(2800),u={},l={},f=e.exports=function(e,t,r,f,p){var h,d,m,g,v=p?function(){return e}:c(e),y=n(r,f,t?2:1),b=0;if("function"!=typeof v)throw TypeError(e+" is not iterable!");if(i(v)){for(h=a(e.length);h>b;b++)if((g=t?y(s(d=e[b])[0],d[1]):y(e[b]))===u||g===l)return g}else for(m=v.call(e);!(d=m.next()).done;)if((g=o(m,y,d.value,t))===u||g===l)return g};f.BREAK=u,f.RETURN=l},2804:e=>{var t=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)},9080:e=>{var t={}.hasOwnProperty;e.exports=function(e,r){return t.call(e,r)}},2336:(e,t,r)=>{var n=r(8520),o=r(8164);e.exports=r(1668)?function(e,t,r){return n.f(e,t,o(1,r))}:function(e,t,r){return e[t]=r,e}},4016:(e,t,r)=>{var n=r(2804).document;e.exports=n&&n.documentElement},5516:(e,t,r)=>{e.exports=!r(1668)&&!r(9316)((function(){return 7!=Object.defineProperty(r(1300)("div"),"a",{get:function(){return 7}}).a}))},2672:(e,t,r)=>{var n=r(3888),o=r(5076).set;e.exports=function(e,t,r){var i,s=t.constructor;return s!==r&&"function"==typeof s&&(i=s.prototype)!==r.prototype&&n(i)&&o&&o(e,i),e}},5628:e=>{e.exports=function(e,t,r){var n=void 0===r;switch(t.length){case 0:return n?e():e.call(r);case 1:return n?e(t[0]):e.call(r,t[0]);case 2:return n?e(t[0],t[1]):e.call(r,t[0],t[1]);case 3:return n?e(t[0],t[1],t[2]):e.call(r,t[0],t[1],t[2]);case 4:return n?e(t[0],t[1],t[2],t[3]):e.call(r,t[0],t[1],t[2],t[3])}return e.apply(r,t)}},2936:(e,t,r)=>{var n=r(6924);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==n(e)?e.split(""):Object(e)}},3164:(e,t,r)=>{var n=r(2488),o=r(7096)("iterator"),i=Array.prototype;e.exports=function(e){return void 0!==e&&(n.Array===e||i[o]===e)}},3888:e=>{e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},7760:(e,t,r)=>{var n=r(3888),o=r(6924),i=r(7096)("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[i])?!!t:"RegExp"==o(e))}},2372:(e,t,r)=>{var n=r(3504);e.exports=function(e,t,r,o){try{return o?t(n(r)[0],r[1]):t(r)}catch(t){var i=e.return;throw void 0!==i&&n(i.call(e)),t}}},128:(e,t,r)=>{"use strict";var n=r(3472),o=r(8164),i=r(6256),s={};r(2336)(s,r(7096)("iterator"),(function(){return this})),e.exports=function(e,t,r){e.prototype=n(s,{next:o(1,r)}),i(e,t+" Iterator")}},6952:(e,t,r)=>{"use strict";var n=r(4440),o=r(7076),i=r(8868),s=r(2336),a=r(2488),c=r(128),u=r(6256),l=r(9556),f=r(7096)("iterator"),p=!([].keys&&"next"in[].keys()),h="keys",d="values",m=function(){return this};e.exports=function(e,t,r,g,v,y,b){c(r,t,g);var w,x,S,P=function(e){if(!p&&e in j)return j[e];switch(e){case h:case d:return function(){return new r(this,e)}}return function(){return new r(this,e)}},O=t+" Iterator",I=v==d,E=!1,j=e.prototype,_=j[f]||j["@@iterator"]||v&&j[v],A=_||P(v),k=v?I?P("entries"):A:void 0,N="Array"==t&&j.entries||_;if(N&&(S=l(N.call(new e)))!==Object.prototype&&S.next&&(u(S,O,!0),n||"function"==typeof S[f]||s(S,f,m)),I&&_&&_.name!==d&&(E=!0,A=function(){return _.call(this)}),n&&!b||!p&&!E&&j[f]||s(j,f,A),a[t]=A,a[O]=m,v)if(w={values:I?A:P(d),keys:y?A:P(h),entries:k},b)for(x in w)x in j||i(j,x,w[x]);else o(o.P+o.F*(p||E),t,w);return w}},8380:(e,t,r)=>{var n=r(7096)("iterator"),o=!1;try{var i=[7][n]();i.return=function(){o=!0},Array.from(i,(function(){throw 2}))}catch(e){}e.exports=function(e,t){if(!t&&!o)return!1;var r=!1;try{var i=[7],s=i[n]();s.next=function(){return{done:r=!0}},i[n]=function(){return s},e(i)}catch(e){}return r}},172:e=>{e.exports=function(e,t){return{value:t,done:!!e}}},2488:e=>{e.exports={}},4440:e=>{e.exports=!1},2020:(e,t,r)=>{var n=r(9664)("meta"),o=r(3888),i=r(9080),s=r(8520).f,a=0,c=Object.isExtensible||function(){return!0},u=!r(9316)((function(){return c(Object.preventExtensions({}))})),l=function(e){s(e,n,{value:{i:"O"+ ++a,w:{}}})},f=e.exports={KEY:n,NEED:!1,fastKey:function(e,t){if(!o(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!i(e,n)){if(!c(e))return"F";if(!t)return"E";l(e)}return e[n].i},getWeak:function(e,t){if(!i(e,n)){if(!c(e))return!0;if(!t)return!1;l(e)}return e[n].w},onFreeze:function(e){return u&&f.NEED&&c(e)&&!i(e,n)&&l(e),e}}},448:(e,t,r)=>{var n=r(2804),o=r(2280).set,i=n.MutationObserver||n.WebKitMutationObserver,s=n.process,a=n.Promise,c="process"==r(6924)(s);e.exports=function(){var e,t,r,u=function(){var n,o;for(c&&(n=s.domain)&&n.exit();e;){o=e.fn,e=e.next;try{o()}catch(n){throw e?r():t=void 0,n}}t=void 0,n&&n.enter()};if(c)r=function(){s.nextTick(u)};else if(!i||n.navigator&&n.navigator.standalone)if(a&&a.resolve){var l=a.resolve(void 0);r=function(){l.then(u)}}else r=function(){o.call(n,u)};else{var f=!0,p=document.createTextNode("");new i(u).observe(p,{characterData:!0}),r=function(){p.data=f=!f}}return function(n){var o={fn:n,next:void 0};t&&(t.next=o),e||(e=o,r()),t=o}}},8024:(e,t,r)=>{"use strict";var n=r(2016);function o(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=n})),this.resolve=n(t),this.reject=n(r)}e.exports.f=function(e){return new o(e)}},6472:(e,t,r)=>{"use strict";var n=r(5444),o=r(5364),i=r(3875),s=r(1164),a=r(2936),c=Object.assign;e.exports=!c||r(9316)((function(){var e={},t={},r=Symbol(),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach((function(e){t[e]=e})),7!=c({},e)[r]||Object.keys(c({},t)).join("")!=n}))?function(e,t){for(var r=s(e),c=arguments.length,u=1,l=o.f,f=i.f;c>u;)for(var p,h=a(arguments[u++]),d=l?n(h).concat(l(h)):n(h),m=d.length,g=0;m>g;)f.call(h,p=d[g++])&&(r[p]=h[p]);return r}:c},3472:(e,t,r)=>{var n=r(3504),o=r(1176),i=r(444),s=r(4588)("IE_PROTO"),a=function(){},c="prototype",u=function(){var e,t=r(1300)("iframe"),n=i.length;for(t.style.display="none",r(4016).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;n--;)delete u[c][i[n]];return u()};e.exports=Object.create||function(e,t){var r;return null!==e?(a[c]=n(e),r=new a,a[c]=null,r[s]=e):r=u(),void 0===t?r:o(r,t)}},8520:(e,t,r)=>{var n=r(3504),o=r(5516),i=r(1896),s=Object.defineProperty;t.f=r(1668)?Object.defineProperty:function(e,t,r){if(n(e),t=i(t,!0),n(r),o)try{return s(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},1176:(e,t,r)=>{var n=r(8520),o=r(3504),i=r(5444);e.exports=r(1668)?Object.defineProperties:function(e,t){o(e);for(var r,s=i(t),a=s.length,c=0;a>c;)n.f(e,r=s[c++],t[r]);return e}},6524:(e,t,r)=>{var n=r(3875),o=r(8164),i=r(2780),s=r(1896),a=r(9080),c=r(5516),u=Object.getOwnPropertyDescriptor;t.f=r(1668)?u:function(e,t){if(e=i(e),t=s(t,!0),c)try{return u(e,t)}catch(e){}if(a(e,t))return o(!n.f.call(e,t),e[t])}},5364:(e,t)=>{t.f=Object.getOwnPropertySymbols},9556:(e,t,r)=>{var n=r(9080),o=r(1164),i=r(4588)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=o(e),n(e,i)?e[i]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},3816:(e,t,r)=>{var n=r(9080),o=r(2780),i=r(7504)(!1),s=r(4588)("IE_PROTO");e.exports=function(e,t){var r,a=o(e),c=0,u=[];for(r in a)r!=s&&n(a,r)&&u.push(r);for(;t.length>c;)n(a,r=t[c++])&&(~i(u,r)||u.push(r));return u}},5444:(e,t,r)=>{var n=r(3816),o=r(444);e.exports=Object.keys||function(e){return n(e,o)}},3875:(e,t)=>{t.f={}.propertyIsEnumerable},2504:(e,t,r)=>{var n=r(5444),o=r(2780),i=r(3875).f;e.exports=function(e){return function(t){for(var r,s=o(t),a=n(s),c=a.length,u=0,l=[];c>u;)i.call(s,r=a[u++])&&l.push(e?[r,s[r]]:s[r]);return l}}},7904:e=>{e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},0:(e,t,r)=>{var n=r(3504),o=r(3888),i=r(8024);e.exports=function(e,t){if(n(e),o(t)&&t.constructor===e)return t;var r=i.f(e);return(0,r.resolve)(t),r.promise}},8164:e=>{e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7704:(e,t,r)=>{var n=r(8868);e.exports=function(e,t,r){for(var o in t)n(e,o,t[o],r);return e}},8868:(e,t,r)=>{var n=r(2804),o=r(2336),i=r(9080),s=r(9664)("src"),a="toString",c=Function[a],u=(""+c).split(a);r(8432).inspectSource=function(e){return c.call(e)},(e.exports=function(e,t,r,a){var c="function"==typeof r;c&&(i(r,"name")||o(r,"name",t)),e[t]!==r&&(c&&(i(r,s)||o(r,s,e[t]?""+e[t]:u.join(String(t)))),e===n?e[t]=r:a?e[t]?e[t]=r:o(e,t,r):(delete e[t],o(e,t,r)))})(Function.prototype,a,(function(){return"function"==typeof this&&this[s]||c.call(this)}))},5076:(e,t,r)=>{var n=r(3888),o=r(3504),i=function(e,t){if(o(e),!n(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{(n=r(800)(Function.call,r(6524).f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,r){return i(e,r),t?e.__proto__=r:n(e,r),e}}({},!1):void 0),check:i}},3384:(e,t,r)=>{"use strict";var n=r(2804),o=r(8520),i=r(1668),s=r(7096)("species");e.exports=function(e){var t=n[e];i&&t&&!t[s]&&o.f(t,s,{configurable:!0,get:function(){return this}})}},6256:(e,t,r)=>{var n=r(8520).f,o=r(9080),i=r(7096)("toStringTag");e.exports=function(e,t,r){e&&!o(e=r?e:e.prototype,i)&&n(e,i,{configurable:!0,value:t})}},4588:(e,t,r)=>{var n=r(5432)("keys"),o=r(9664);e.exports=function(e){return n[e]||(n[e]=o(e))}},5432:(e,t,r)=>{var n=r(8432),o=r(2804),i="__core-js_shared__",s=o[i]||(o[i]={});(e.exports=function(e,t){return s[e]||(s[e]=void 0!==t?t:{})})("versions",[]).push({version:n.version,mode:r(4440)?"pure":"global",copyright:"© 2018 Denis Pushkarev (zloirock.ru)"})},6776:(e,t,r)=>{var n=r(3504),o=r(2016),i=r(7096)("species");e.exports=function(e,t){var r,s=n(e).constructor;return void 0===s||null==(r=n(s)[i])?t:o(r)}},6712:(e,t,r)=>{"use strict";var n=r(9316);e.exports=function(e,t){return!!e&&n((function(){t?e.call(null,(function(){}),1):e.call(null)}))}},2280:(e,t,r)=>{var n,o,i,s=r(800),a=r(5628),c=r(4016),u=r(1300),l=r(2804),f=l.process,p=l.setImmediate,h=l.clearImmediate,d=l.MessageChannel,m=l.Dispatch,g=0,v={},y="onreadystatechange",b=function(){var e=+this;if(v.hasOwnProperty(e)){var t=v[e];delete v[e],t()}},w=function(e){b.call(e.data)};p&&h||(p=function(e){for(var t=[],r=1;arguments.length>r;)t.push(arguments[r++]);return v[++g]=function(){a("function"==typeof e?e:Function(e),t)},n(g),g},h=function(e){delete v[e]},"process"==r(6924)(f)?n=function(e){f.nextTick(s(b,e,1))}:m&&m.now?n=function(e){m.now(s(b,e,1))}:d?(i=(o=new d).port2,o.port1.onmessage=w,n=s(i.postMessage,i,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(n=function(e){l.postMessage(e+"","*")},l.addEventListener("message",w,!1)):n=y in u("script")?function(e){c.appendChild(u("script"))[y]=function(){c.removeChild(this),b.call(e)}}:function(e){setTimeout(s(b,e,1),0)}),e.exports={set:p,clear:h}},8508:(e,t,r)=>{var n=r(8236),o=Math.max,i=Math.min;e.exports=function(e,t){return(e=n(e))<0?o(e+t,0):i(e,t)}},8236:e=>{var t=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:t)(e)}},2780:(e,t,r)=>{var n=r(2936),o=r(24);e.exports=function(e){return n(o(e))}},3528:(e,t,r)=>{var n=r(8236),o=Math.min;e.exports=function(e){return e>0?o(n(e),9007199254740991):0}},1164:(e,t,r)=>{var n=r(24);e.exports=function(e){return Object(n(e))}},1896:(e,t,r)=>{var n=r(3888);e.exports=function(e,t){if(!n(e))return e;var r,o;if(t&&"function"==typeof(r=e.toString)&&!n(o=r.call(e)))return o;if("function"==typeof(r=e.valueOf)&&!n(o=r.call(e)))return o;if(!t&&"function"==typeof(r=e.toString)&&!n(o=r.call(e)))return o;throw TypeError("Can't convert object to primitive value")}},9664:e=>{var t=0,r=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++t+r).toString(36))}},1168:(e,t,r)=>{var n=r(2804).navigator;e.exports=n&&n.userAgent||""},2772:(e,t,r)=>{var n=r(3888);e.exports=function(e,t){if(!n(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},7096:(e,t,r)=>{var n=r(5432)("wks"),o=r(9664),i=r(2804).Symbol,s="function"==typeof i;(e.exports=function(e){return n[e]||(n[e]=s&&i[e]||(s?i:o)("Symbol."+e))}).store=n},2800:(e,t,r)=>{var n=r(5848),o=r(7096)("iterator"),i=r(2488);e.exports=r(8432).getIteratorMethod=function(e){if(null!=e)return e[o]||e["@@iterator"]||i[n(e)]}},4228:(e,t,r)=>{"use strict";var n=r(800),o=r(7076),i=r(1164),s=r(2372),a=r(3164),c=r(3528),u=r(2340),l=r(2800);o(o.S+o.F*!r(8380)((function(e){Array.from(e)})),"Array",{from:function(e){var t,r,o,f,p=i(e),h="function"==typeof this?this:Array,d=arguments.length,m=d>1?arguments[1]:void 0,g=void 0!==m,v=0,y=l(p);if(g&&(m=n(m,d>2?arguments[2]:void 0,2)),null==y||h==Array&&a(y))for(r=new h(t=c(p.length));t>v;v++)u(r,v,g?m(p[v],v):p[v]);else for(f=y.call(p),r=new h;!(o=f.next()).done;v++)u(r,v,g?s(f,m,[o.value,v],!0):o.value);return r.length=v,r}})},4260:(e,t,r)=>{"use strict";var n=r(7076),o=r(2016),i=r(1164),s=r(9316),a=[].sort,c=[1,2,3];n(n.P+n.F*(s((function(){c.sort(void 0)}))||!s((function(){c.sort(null)}))||!r(6712)(a)),"Array",{sort:function(e){return void 0===e?a.call(i(this)):a.call(i(this),o(e))}})},5584:(e,t,r)=>{var n=r(7076);n(n.S+n.F,"Object",{assign:r(6472)})},5888:(e,t,r)=>{"use strict";var n,o,i,s,a=r(4440),c=r(2804),u=r(800),l=r(5848),f=r(7076),p=r(3888),h=r(2016),d=r(2388),m=r(7228),g=r(6776),v=r(2280).set,y=r(448)(),b=r(8024),w=r(7904),x=r(1168),S=r(0),P="Promise",O=c.TypeError,I=c.process,E=I&&I.versions,j=E&&E.v8||"",_=c[P],A="process"==l(I),k=function(){},N=o=b.f,F=!!function(){try{var e=_.resolve(1),t=(e.constructor={})[r(7096)("species")]=function(e){e(k,k)};return(A||"function"==typeof PromiseRejectionEvent)&&e.then(k)instanceof t&&0!==j.indexOf("6.6")&&-1===x.indexOf("Chrome/66")}catch(e){}}(),C=function(e){var t;return!(!p(e)||"function"!=typeof(t=e.then))&&t},M=function(e,t){if(!e._n){e._n=!0;var r=e._c;y((function(){for(var n=e._v,o=1==e._s,i=0,s=function(t){var r,i,s,a=o?t.ok:t.fail,c=t.resolve,u=t.reject,l=t.domain;try{a?(o||(2==e._h&&D(e),e._h=1),!0===a?r=n:(l&&l.enter(),r=a(n),l&&(l.exit(),s=!0)),r===t.promise?u(O("Promise-chain cycle")):(i=C(r))?i.call(r,c,u):c(r)):u(n)}catch(e){l&&!s&&l.exit(),u(e)}};r.length>i;)s(r[i++]);e._c=[],e._n=!1,t&&!e._h&&V(e)}))}},V=function(e){v.call(c,(function(){var t,r,n,o=e._v,i=T(e);if(i&&(t=w((function(){A?I.emit("unhandledRejection",o,e):(r=c.onunhandledrejection)?r({promise:e,reason:o}):(n=c.console)&&n.error&&n.error("Unhandled promise rejection",o)})),e._h=A||T(e)?2:1),e._a=void 0,i&&t.e)throw t.v}))},T=function(e){return 1!==e._h&&0===(e._a||e._c).length},D=function(e){v.call(c,(function(){var t;A?I.emit("rejectionHandled",e):(t=c.onrejectionhandled)&&t({promise:e,reason:e._v})}))},L=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),M(t,!0))},$=function(e){var t,r=this;if(!r._d){r._d=!0,r=r._w||r;try{if(r===e)throw O("Promise can't be resolved itself");(t=C(e))?y((function(){var n={_w:r,_d:!1};try{t.call(e,u($,n,1),u(L,n,1))}catch(e){L.call(n,e)}})):(r._v=e,r._s=1,M(r,!1))}catch(e){L.call({_w:r,_d:!1},e)}}};F||(_=function(e){d(this,_,P,"_h"),h(e),n.call(this);try{e(u($,this,1),u(L,this,1))}catch(e){L.call(this,e)}},(n=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=r(7704)(_.prototype,{then:function(e,t){var r=N(g(this,_));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=A?I.domain:void 0,this._c.push(r),this._a&&this._a.push(r),this._s&&M(this,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),i=function(){var e=new n;this.promise=e,this.resolve=u($,e,1),this.reject=u(L,e,1)},b.f=N=function(e){return e===_||e===s?new i(e):o(e)}),f(f.G+f.W+f.F*!F,{Promise:_}),r(6256)(_,P),r(3384)(P),s=r(8432)[P],f(f.S+f.F*!F,P,{reject:function(e){var t=N(this);return(0,t.reject)(e),t.promise}}),f(f.S+f.F*(a||!F),P,{resolve:function(e){return S(a&&this===s?_:this,e)}}),f(f.S+f.F*!(F&&r(8380)((function(e){_.all(e).catch(k)}))),P,{all:function(e){var t=this,r=N(t),n=r.resolve,o=r.reject,i=w((function(){var r=[],i=0,s=1;m(e,!1,(function(e){var a=i++,c=!1;r.push(void 0),s++,t.resolve(e).then((function(e){c||(c=!0,r[a]=e,--s||n(r))}),o)})),--s||n(r)}));return i.e&&o(i.v),r.promise},race:function(e){var t=this,r=N(t),n=r.reject,o=w((function(){m(e,!1,(function(e){t.resolve(e).then(r.resolve,n)}))}));return o.e&&n(o.v),r.promise}})},7320:(e,t,r)=>{r(1668)&&"g"!=/./g.flags&&r(8520).f(RegExp.prototype,"flags",{configurable:!0,get:r(8068)})},4880:(e,t,r)=>{r(4448)("match",1,(function(e,t,r){return[function(r){"use strict";var n=e(this),o=null==r?void 0:r[t];return void 0!==o?o.call(r,n):new RegExp(r)[t](String(n))},r]}))},8412:(e,t,r)=>{r(4448)("replace",2,(function(e,t,r){return[function(n,o){"use strict";var i=e(this),s=null==n?void 0:n[t];return void 0!==s?s.call(n,i,o):r.call(String(i),n,o)},r]}))},223:(e,t,r)=>{r(4448)("search",1,(function(e,t,r){return[function(r){"use strict";var n=e(this),o=null==r?void 0:r[t];return void 0!==o?o.call(r,n):new RegExp(r)[t](String(n))},r]}))},3708:(e,t,r)=>{r(4448)("split",2,(function(e,t,n){"use strict";var o=r(7760),i=n,s=[].push,a="split",c="length",u="lastIndex";if("c"=="abbc"[a](/(b)*/)[1]||4!="test"[a](/(?:)/,-1)[c]||2!="ab"[a](/(?:ab)*/)[c]||4!="."[a](/(.?)(.?)/)[c]||"."[a](/()()/)[c]>1||""[a](/.?/)[c]){var l=void 0===/()??/.exec("")[1];n=function(e,t){var r=String(this);if(void 0===e&&0===t)return[];if(!o(e))return i.call(r,e,t);var n,a,f,p,h,d=[],m=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),g=0,v=void 0===t?4294967295:t>>>0,y=new RegExp(e.source,m+"g");for(l||(n=new RegExp("^"+y.source+"$(?!\\s)",m));(a=y.exec(r))&&!((f=a.index+a[0][c])>g&&(d.push(r.slice(g,a.index)),!l&&a[c]>1&&a[0].replace(n,(function(){for(h=1;h<arguments[c]-2;h++)void 0===arguments[h]&&(a[h]=void 0)})),a[c]>1&&a.index<r[c]&&s.apply(d,a.slice(1)),p=a[0][c],g=f,d[c]>=v));)y[u]===a.index&&y[u]++;return g===r[c]?!p&&y.test("")||d.push(""):d.push(r.slice(g)),d[c]>v?d.slice(0,v):d}}else"0"[a](void 0,0)[c]&&(n=function(e,t){return void 0===e&&0===t?[]:i.call(this,e,t)});return[function(r,o){var i=e(this),s=null==r?void 0:r[t];return void 0!==s?s.call(r,i,o):n.call(String(i),r,o)},n]}))},6568:(e,t,r)=>{"use strict";r(7320);var n=r(3504),o=r(8068),i=r(1668),s="toString",a=/./[s],c=function(e){r(8868)(RegExp.prototype,s,e,!0)};r(9316)((function(){return"/a/b"!=a.call({source:"a",flags:"b"})}))?c((function(){var e=n(this);return"/".concat(e.source,"/","flags"in e?e.flags:!i&&e instanceof RegExp?o.call(e):void 0)})):a.name!=s&&c((function(){return a.call(this)}))},7276:(e,t,r)=>{"use strict";var n=r(148),o=r(2772);e.exports=r(9412)("Set",(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(e){return n.def(o(this,"Set"),e=0===e?0:e,e)}},n)},7360:(e,t,r)=>{"use strict";var n=r(7076),o=r(7504)(!0);n(n.P,"Array",{includes:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),r(7384)("includes")},3852:(e,t,r)=>{var n=r(7076),o=r(2504)(!0);n(n.S,"Object",{entries:function(e){return o(e)}})},5308:(e,t,r)=>{var n=r(7076),o=r(2504)(!1);n(n.S,"Object",{values:function(e){return o(e)}})},7468:(e,t,r)=>{"use strict";const n=r(4368),o=r(9624),i=r(1846);function s(e,t,r){const s=o(e,t,r),a=n.spawn(s.command,s.args,s.options);return i.hookChildProcess(a,s),a}e.exports=s,e.exports.spawn=s,e.exports.sync=function(e,t,r){const s=o(e,t,r),a=n.spawnSync(s.command,s.args,s.options);return a.error=a.error||i.verifyENOENTSync(a.status,s),a},e.exports._parse=o,e.exports._enoent=i},1846:e=>{"use strict";const t="win32"===process.platform;function r(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function n(e,n){return t&&1===e&&!n.file?r(n.original,"spawn"):null}e.exports={hookChildProcess:function(e,r){if(!t)return;const o=e.emit;e.emit=function(t,i){if("exit"===t){const t=n(i,r);if(t)return o.call(e,"error",t)}return o.apply(e,arguments)}},verifyENOENT:n,verifyENOENTSync:function(e,n){return t&&1===e&&!n.file?r(n.original,"spawnSync"):null},notFoundError:r}},9624:(e,t,r)=>{"use strict";const n=r(7072),o=r(9480),i=r(8372),s=r(824),a=r(7780),c=r(1716),u="win32"===process.platform,l=/\.(?:com|exe)$/i,f=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i,p=o((()=>c.satisfies(process.version,"^4.8.0 || ^5.7.0 || >= 6.0.0",!0)))||!1;e.exports=function(e,t,r){t&&!Array.isArray(t)&&(r=t,t=null);const o={command:e,args:t=t?t.slice(0):[],options:r=Object.assign({},r),file:void 0,original:{command:e,args:t}};return r.shell?function(e){if(p)return e;const t=[e.command].concat(e.args).join(" ");return u?(e.command="string"==typeof e.options.shell?e.options.shell:process.env.comspec||"cmd.exe",e.args=["/d","/s","/c",`"${t}"`],e.options.windowsVerbatimArguments=!0):("string"==typeof e.options.shell?e.command=e.options.shell:"android"===process.platform?e.command="/system/bin/sh":e.command="/bin/sh",e.args=["-c",t]),e}(o):function(e){if(!u)return e;const t=function(e){e.file=i(e);const t=e.file&&a(e.file);return t?(e.args.unshift(e.file),e.command=t,i(e)):e.file}(e),r=!l.test(t);if(e.options.forceShell||r){const r=f.test(t);e.command=n.normalize(e.command),e.command=s.command(e.command),e.args=e.args.map((e=>s.argument(e,r)));const o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`],e.command=process.env.comspec||"cmd.exe",e.options.windowsVerbatimArguments=!0}return e}(o)}},824:e=>{"use strict";const t=/([()\][%!^"`<>&|;, *?])/g;e.exports.command=function(e){return e.replace(t,"^$1")},e.exports.argument=function(e,r){return e=(e=`"${e=(e=(e=`${e}`).replace(/(\\*)"/g,'$1$1\\"')).replace(/(\\*)$/,"$1$1")}"`).replace(t,"^$1"),r&&(e=e.replace(t,"^$1")),e}},7780:(e,t,r)=>{"use strict";const n=r(2058),o=r(6176);e.exports=function(e){let t,r;Buffer.alloc?t=Buffer.alloc(150):(t=new Buffer(150),t.fill(0));try{r=n.openSync(e,"r"),n.readSync(r,t,0,150,0),n.closeSync(r)}catch(e){}return o(t.toString())}},8372:(e,t,r)=>{"use strict";const n=r(7072),o=r(8460),i=r(3032)();function s(e,t){const r=process.cwd(),s=null!=e.options.cwd;if(s)try{process.chdir(e.options.cwd)}catch(e){}let a;try{a=o.sync(e.command,{path:(e.options.env||process.env)[i],pathExt:t?n.delimiter:void 0})}catch(e){}finally{process.chdir(r)}return a&&(a=n.resolve(s?e.options.cwd:"",a)),a}e.exports=function(e){return s(e)||s(e,!0)}},9988:(e,t,r)=>{var n=r(4640),o=function(){},i=function(e,t,r){if("function"==typeof t)return i(e,null,t);t||(t={}),r=n(r||o);var s=e._writableState,a=e._readableState,c=t.readable||!1!==t.readable&&e.readable,u=t.writable||!1!==t.writable&&e.writable,l=function(){e.writable||f()},f=function(){u=!1,c||r.call(e)},p=function(){c=!1,u||r.call(e)},h=function(t){r.call(e,t?new Error("exited with error code: "+t):null)},d=function(t){r.call(e,t)},m=function(){return(!c||a&&a.ended)&&(!u||s&&s.ended)?void 0:r.call(e,new Error("premature close"))},g=function(){e.req.on("finish",f)};return function(e){return e.setHeader&&"function"==typeof e.abort}(e)?(e.on("complete",f),e.on("abort",m),e.req?g():e.on("request",g)):u&&!s&&(e.on("end",l),e.on("close",l)),function(e){return e.stdio&&Array.isArray(e.stdio)&&3===e.stdio.length}(e)&&e.on("exit",h),e.on("end",p),e.on("finish",f),!1!==t.error&&e.on("error",d),e.on("close",m),function(){e.removeListener("complete",f),e.removeListener("abort",m),e.removeListener("request",g),e.req&&e.req.removeListener("finish",f),e.removeListener("end",l),e.removeListener("close",l),e.removeListener("finish",f),e.removeListener("exit",h),e.removeListener("end",p),e.removeListener("error",d),e.removeListener("close",m)}};e.exports=i},84:(e,t,r)=>{e.exports=l,l.realpath=l,l.sync=f,l.realpathSync=f,l.monkeypatch=function(){n.realpath=l,n.realpathSync=f},l.unmonkeypatch=function(){n.realpath=o,n.realpathSync=i};var n=r(2058),o=n.realpath,i=n.realpathSync,s=process.version,a=/^v[0-5]\./.test(s),c=r(7192);function u(e){return e&&"realpath"===e.syscall&&("ELOOP"===e.code||"ENOMEM"===e.code||"ENAMETOOLONG"===e.code)}function l(e,t,r){if(a)return o(e,t,r);"function"==typeof t&&(r=t,t=null),o(e,t,(function(n,o){u(n)?c.realpath(e,t,r):r(n,o)}))}function f(e,t){if(a)return i(e,t);try{return i(e,t)}catch(r){if(u(r))return c.realpathSync(e,t);throw r}}},7192:(e,t,r)=>{var n=r(7072),o="win32"===process.platform,i=r(2058),s=process.env.NODE_DEBUG&&/fs/.test(process.env.NODE_DEBUG);if(n.normalize,o)var a=/(.*?)(?:[\/\\]+|$)/g;else a=/(.*?)(?:[\/]+|$)/g;if(o)var c=/^(?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/][^\\\/]+)?[\\\/]*/;else c=/^[\/]*/;t.realpathSync=function(e,t){if(e=n.resolve(e),t&&Object.prototype.hasOwnProperty.call(t,e))return t[e];var r,s,u,l,f=e,p={},h={};function d(){var t=c.exec(e);r=t[0].length,s=t[0],u=t[0],l="",o&&!h[u]&&(i.lstatSync(u),h[u]=!0)}for(d();r<e.length;){a.lastIndex=r;var m=a.exec(e);if(l=s,s+=m[0],u=l+m[1],r=a.lastIndex,!(h[u]||t&&t[u]===u)){var g;if(t&&Object.prototype.hasOwnProperty.call(t,u))g=t[u];else{var v=i.lstatSync(u);if(!v.isSymbolicLink()){h[u]=!0,t&&(t[u]=u);continue}var y=null;if(!o){var b=v.dev.toString(32)+":"+v.ino.toString(32);p.hasOwnProperty(b)&&(y=p[b])}null===y&&(i.statSync(u),y=i.readlinkSync(u)),g=n.resolve(l,y),t&&(t[u]=g),o||(p[b]=y)}e=n.resolve(g,e.slice(r)),d()}}return t&&(t[f]=e),e},t.realpath=function(e,t,r){if("function"!=typeof r&&(r=function(e){return"function"==typeof e?e:function(){var e;if(s){var t=new Error;e=function(e){e&&(t.message=e.message,r(e=t))}}else e=r;return e;function r(e){if(e){if(process.throwDeprecation)throw e;if(!process.noDeprecation){var t="fs: missing callback "+(e.stack||e.message);process.traceDeprecation?console.trace(t):console.error(t)}}}}()}(t),t=null),e=n.resolve(e),t&&Object.prototype.hasOwnProperty.call(t,e))return process.nextTick(r.bind(null,null,t[e]));var u,l,f,p,h=e,d={},m={};function g(){var t=c.exec(e);u=t[0].length,l=t[0],f=t[0],p="",o&&!m[f]?i.lstat(f,(function(e){if(e)return r(e);m[f]=!0,v()})):process.nextTick(v)}function v(){if(u>=e.length)return t&&(t[h]=e),r(null,e);a.lastIndex=u;var n=a.exec(e);return p=l,l+=n[0],f=p+n[1],u=a.lastIndex,m[f]||t&&t[f]===f?process.nextTick(v):t&&Object.prototype.hasOwnProperty.call(t,f)?w(t[f]):i.lstat(f,y)}function y(e,n){if(e)return r(e);if(!n.isSymbolicLink())return m[f]=!0,t&&(t[f]=f),process.nextTick(v);if(!o){var s=n.dev.toString(32)+":"+n.ino.toString(32);if(d.hasOwnProperty(s))return b(null,d[s],f)}i.stat(f,(function(e){if(e)return r(e);i.readlink(f,(function(e,t){o||(d[s]=t),b(e,t)}))}))}function b(e,o,i){if(e)return r(e);var s=n.resolve(p,o);t&&(t[i]=s),w(s)}function w(t){e=n.resolve(t,e.slice(u)),g()}g()}},9712:(e,t,r)=>{function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.alphasort=u,t.alphasorti=c,t.setopts=function(e,t,r){if(r||(r={}),r.matchBase&&-1===t.indexOf("/")){if(r.noglobstar)throw new Error("base matching requires globstar");t="**/"+t}e.silent=!!r.silent,e.pattern=t,e.strict=!1!==r.strict,e.realpath=!!r.realpath,e.realpathCache=r.realpathCache||Object.create(null),e.follow=!!r.follow,e.dot=!!r.dot,e.mark=!!r.mark,e.nodir=!!r.nodir,e.nodir&&(e.mark=!0),e.sync=!!r.sync,e.nounique=!!r.nounique,e.nonull=!!r.nonull,e.nosort=!!r.nosort,e.nocase=!!r.nocase,e.stat=!!r.stat,e.noprocess=!!r.noprocess,e.absolute=!!r.absolute,e.maxLength=r.maxLength||1/0,e.cache=r.cache||Object.create(null),e.statCache=r.statCache||Object.create(null),e.symlinks=r.symlinks||Object.create(null),function(e,t){e.ignore=t.ignore||[],Array.isArray(e.ignore)||(e.ignore=[e.ignore]),e.ignore.length&&(e.ignore=e.ignore.map(l))}(e,r),e.changedCwd=!1;var i=process.cwd();n(r,"cwd")?(e.cwd=o.resolve(r.cwd),e.changedCwd=e.cwd!==i):e.cwd=i,e.root=r.root||o.resolve(e.cwd,"/"),e.root=o.resolve(e.root),"win32"===process.platform&&(e.root=e.root.replace(/\\/g,"/")),e.cwdAbs=s(e.cwd)?e.cwd:f(e,e.cwd),"win32"===process.platform&&(e.cwdAbs=e.cwdAbs.replace(/\\/g,"/")),e.nomount=!!r.nomount,r.nonegate=!0,r.nocomment=!0,e.minimatch=new a(t,r),e.options=e.minimatch.options},t.ownProp=n,t.makeAbs=f,t.finish=function(e){for(var t=e.nounique,r=t?[]:Object.create(null),n=0,o=e.matches.length;n<o;n++){var i=e.matches[n];if(i&&0!==Object.keys(i).length){var s=Object.keys(i);t?r.push.apply(r,s):s.forEach((function(e){r[e]=!0}))}else if(e.nonull){var a=e.minimatch.globSet[n];t?r.push(a):r[a]=!0}}if(t||(r=Object.keys(r)),e.nosort||(r=r.sort(e.nocase?c:u)),e.mark){for(n=0;n<r.length;n++)r[n]=e._mark(r[n]);e.nodir&&(r=r.filter((function(t){var r=!/\/$/.test(t),n=e.cache[t]||e.cache[f(e,t)];return r&&n&&(r="DIR"!==n&&!Array.isArray(n)),r})))}e.ignore.length&&(r=r.filter((function(t){return!p(e,t)}))),e.found=r},t.mark=function(e,t){var r=f(e,t),n=e.cache[r],o=t;if(n){var i="DIR"===n||Array.isArray(n),s="/"===t.slice(-1);if(i&&!s?o+="/":!i&&s&&(o=o.slice(0,-1)),o!==t){var a=f(e,o);e.statCache[a]=e.statCache[r],e.cache[a]=e.cache[r]}}return o},t.isIgnored=p,t.childrenIgnored=function(e,t){return!!e.ignore.length&&e.ignore.some((function(e){return!(!e.gmatcher||!e.gmatcher.match(t))}))};var o=r(7072),i=r(836),s=r(4832),a=i.Minimatch;function c(e,t){return e.toLowerCase().localeCompare(t.toLowerCase())}function u(e,t){return e.localeCompare(t)}function l(e){var t=null;if("/**"===e.slice(-3)){var r=e.replace(/(\/\*\*)+$/,"");t=new a(r,{dot:!0})}return{matcher:new a(e,{dot:!0}),gmatcher:t}}function f(e,t){var r=t;return r="/"===t.charAt(0)?o.join(e.root,t):s(t)||""===t?t:e.changedCwd?o.resolve(e.cwd,t):o.resolve(t),"win32"===process.platform&&(r=r.replace(/\\/g,"/")),r}function p(e,t){return!!e.ignore.length&&e.ignore.some((function(e){return e.matcher.match(t)||!(!e.gmatcher||!e.gmatcher.match(t))}))}},808:(e,t,r)=>{e.exports=b;var n=r(2058),o=r(84),i=r(836),s=(i.Minimatch,r(3352)),a=r(467).EventEmitter,c=r(7072),u=r(6608),l=r(4832),f=r(4548),p=r(9712),h=(p.alphasort,p.alphasorti,p.setopts),d=p.ownProp,m=r(363),g=(r(970),p.childrenIgnored),v=p.isIgnored,y=r(4640);function b(e,t,r){if("function"==typeof t&&(r=t,t={}),t||(t={}),t.sync){if(r)throw new TypeError("callback provided to sync glob");return f(e,t)}return new x(e,t,r)}b.sync=f;var w=b.GlobSync=f.GlobSync;function x(e,t,r){if("function"==typeof t&&(r=t,t=null),t&&t.sync){if(r)throw new TypeError("callback provided to sync glob");return new w(e,t)}if(!(this instanceof x))return new x(e,t,r);h(this,e,t),this._didRealPath=!1;var n=this.minimatch.set.length;this.matches=new Array(n),"function"==typeof r&&(r=y(r),this.on("error",r),this.on("end",(function(e){r(null,e)})));var o=this;if(this._processing=0,this._emitQueue=[],this._processQueue=[],this.paused=!1,this.noprocess)return this;if(0===n)return a();for(var i=!0,s=0;s<n;s++)this._process(this.minimatch.set[s],s,!1,a);function a(){--o._processing,o._processing<=0&&(i?process.nextTick((function(){o._finish()})):o._finish())}i=!1}b.glob=b,b.hasMagic=function(e,t){var r=function(e,t){if(null===t||"object"!=typeof t)return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e}({},t);r.noprocess=!0;var n=new x(e,r).minimatch.set;if(!e)return!1;if(n.length>1)return!0;for(var o=0;o<n[0].length;o++)if("string"!=typeof n[0][o])return!0;return!1},b.Glob=x,s(x,a),x.prototype._finish=function(){if(u(this instanceof x),!this.aborted){if(this.realpath&&!this._didRealpath)return this._realpath();p.finish(this),this.emit("end",this.found)}},x.prototype._realpath=function(){if(!this._didRealpath){this._didRealpath=!0;var e=this.matches.length;if(0===e)return this._finish();for(var t=this,r=0;r<this.matches.length;r++)this._realpathSet(r,n)}function n(){0==--e&&t._finish()}},x.prototype._realpathSet=function(e,t){var r=this.matches[e];if(!r)return t();var n=Object.keys(r),i=this,s=n.length;if(0===s)return t();var a=this.matches[e]=Object.create(null);n.forEach((function(r,n){r=i._makeAbs(r),o.realpath(r,i.realpathCache,(function(n,o){n?"stat"===n.syscall?a[r]=!0:i.emit("error",n):a[o]=!0,0==--s&&(i.matches[e]=a,t())}))}))},x.prototype._mark=function(e){return p.mark(this,e)},x.prototype._makeAbs=function(e){return p.makeAbs(this,e)},x.prototype.abort=function(){this.aborted=!0,this.emit("abort")},x.prototype.pause=function(){this.paused||(this.paused=!0,this.emit("pause"))},x.prototype.resume=function(){if(this.paused){if(this.emit("resume"),this.paused=!1,this._emitQueue.length){var e=this._emitQueue.slice(0);this._emitQueue.length=0;for(var t=0;t<e.length;t++){var r=e[t];this._emitMatch(r[0],r[1])}}if(this._processQueue.length){var n=this._processQueue.slice(0);for(this._processQueue.length=0,t=0;t<n.length;t++){var o=n[t];this._processing--,this._process(o[0],o[1],o[2],o[3])}}}},x.prototype._process=function(e,t,r,n){if(u(this instanceof x),u("function"==typeof n),!this.aborted)if(this._processing++,this.paused)this._processQueue.push([e,t,r,n]);else{for(var o,s=0;"string"==typeof e[s];)s++;switch(s){case e.length:return void this._processSimple(e.join("/"),t,n);case 0:o=null;break;default:o=e.slice(0,s).join("/")}var a,c=e.slice(s);null===o?a=".":l(o)||l(e.join("/"))?(o&&l(o)||(o="/"+o),a=o):a=o;var f=this._makeAbs(a);if(g(this,a))return n();c[0]===i.GLOBSTAR?this._processGlobStar(o,a,f,c,t,r,n):this._processReaddir(o,a,f,c,t,r,n)}},x.prototype._processReaddir=function(e,t,r,n,o,i,s){var a=this;this._readdir(r,i,(function(c,u){return a._processReaddir2(e,t,r,n,o,i,u,s)}))},x.prototype._processReaddir2=function(e,t,r,n,o,i,s,a){if(!s)return a();for(var u=n[0],l=!!this.minimatch.negate,f=u._glob,p=this.dot||"."===f.charAt(0),h=[],d=0;d<s.length;d++)("."!==(g=s[d]).charAt(0)||p)&&(l&&!e?!g.match(u):g.match(u))&&h.push(g);var m=h.length;if(0===m)return a();if(1===n.length&&!this.mark&&!this.stat){for(this.matches[o]||(this.matches[o]=Object.create(null)),d=0;d<m;d++){var g=h[d];e&&(g="/"!==e?e+"/"+g:e+g),"/"!==g.charAt(0)||this.nomount||(g=c.join(this.root,g)),this._emitMatch(o,g)}return a()}for(n.shift(),d=0;d<m;d++)g=h[d],e&&(g="/"!==e?e+"/"+g:e+g),this._process([g].concat(n),o,i,a);a()},x.prototype._emitMatch=function(e,t){if(!this.aborted&&!v(this,t))if(this.paused)this._emitQueue.push([e,t]);else{var r=l(t)?t:this._makeAbs(t);if(this.mark&&(t=this._mark(t)),this.absolute&&(t=r),!this.matches[e][t]){if(this.nodir){var n=this.cache[r];if("DIR"===n||Array.isArray(n))return}this.matches[e][t]=!0;var o=this.statCache[r];o&&this.emit("stat",t,o),this.emit("match",t)}}},x.prototype._readdirInGlobStar=function(e,t){if(!this.aborted){if(this.follow)return this._readdir(e,!1,t);var r=this,o=m("lstat\0"+e,(function(n,o){if(n&&"ENOENT"===n.code)return t();var i=o&&o.isSymbolicLink();r.symlinks[e]=i,i||!o||o.isDirectory()?r._readdir(e,!1,t):(r.cache[e]="FILE",t())}));o&&n.lstat(e,o)}},x.prototype._readdir=function(e,t,r){if(!this.aborted&&(r=m("readdir\0"+e+"\0"+t,r))){if(t&&!d(this.symlinks,e))return this._readdirInGlobStar(e,r);if(d(this.cache,e)){var o=this.cache[e];if(!o||"FILE"===o)return r();if(Array.isArray(o))return r(null,o)}n.readdir(e,function(e,t,r){return function(n,o){n?e._readdirError(t,n,r):e._readdirEntries(t,o,r)}}(this,e,r))}},x.prototype._readdirEntries=function(e,t,r){if(!this.aborted){if(!this.mark&&!this.stat)for(var n=0;n<t.length;n++){var o=t[n];o="/"===e?e+o:e+"/"+o,this.cache[o]=!0}return this.cache[e]=t,r(null,t)}},x.prototype._readdirError=function(e,t,r){if(!this.aborted){switch(t.code){case"ENOTSUP":case"ENOTDIR":var n=this._makeAbs(e);if(this.cache[n]="FILE",n===this.cwdAbs){var o=new Error(t.code+" invalid cwd "+this.cwd);o.path=this.cwd,o.code=t.code,this.emit("error",o),this.abort()}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=!1;break;default:this.cache[this._makeAbs(e)]=!1,this.strict&&(this.emit("error",t),this.abort()),this.silent||console.error("glob error",t)}return r()}},x.prototype._processGlobStar=function(e,t,r,n,o,i,s){var a=this;this._readdir(r,i,(function(c,u){a._processGlobStar2(e,t,r,n,o,i,u,s)}))},x.prototype._processGlobStar2=function(e,t,r,n,o,i,s,a){if(!s)return a();var c=n.slice(1),u=e?[e]:[],l=u.concat(c);this._process(l,o,!1,a);var f=this.symlinks[r],p=s.length;if(f&&i)return a();for(var h=0;h<p;h++)if("."!==s[h].charAt(0)||this.dot){var d=u.concat(s[h],c);this._process(d,o,!0,a);var m=u.concat(s[h],n);this._process(m,o,!0,a)}a()},x.prototype._processSimple=function(e,t,r){var n=this;this._stat(e,(function(o,i){n._processSimple2(e,t,o,i,r)}))},x.prototype._processSimple2=function(e,t,r,n,o){if(this.matches[t]||(this.matches[t]=Object.create(null)),!n)return o();if(e&&l(e)&&!this.nomount){var i=/[\/\\]$/.test(e);"/"===e.charAt(0)?e=c.join(this.root,e):(e=c.resolve(this.root,e),i&&(e+="/"))}"win32"===process.platform&&(e=e.replace(/\\/g,"/")),this._emitMatch(t,e),o()},x.prototype._stat=function(e,t){var r=this._makeAbs(e),o="/"===e.slice(-1);if(e.length>this.maxLength)return t();if(!this.stat&&d(this.cache,r)){var i=this.cache[r];if(Array.isArray(i)&&(i="DIR"),!o||"DIR"===i)return t(null,i);if(o&&"FILE"===i)return t()}var s=this.statCache[r];if(void 0!==s){if(!1===s)return t(null,s);var a=s.isDirectory()?"DIR":"FILE";return o&&"FILE"===a?t():t(null,a,s)}var c=this,u=m("stat\0"+r,(function(o,i){if(i&&i.isSymbolicLink())return n.stat(r,(function(n,o){n?c._stat2(e,r,null,i,t):c._stat2(e,r,n,o,t)}));c._stat2(e,r,o,i,t)}));u&&n.lstat(r,u)},x.prototype._stat2=function(e,t,r,n,o){if(r&&("ENOENT"===r.code||"ENOTDIR"===r.code))return this.statCache[t]=!1,o();var i="/"===e.slice(-1);if(this.statCache[t]=n,"/"===t.slice(-1)&&n&&!n.isDirectory())return o(null,!1,n);var s=!0;return n&&(s=n.isDirectory()?"DIR":"FILE"),this.cache[t]=this.cache[t]||s,i&&"FILE"===s?o():o(null,s,n)}},4548:(e,t,r)=>{e.exports=d,d.GlobSync=m;var n=r(2058),o=r(84),i=r(836),s=(i.Minimatch,r(808).Glob,r(970),r(7072)),a=r(6608),c=r(4832),u=r(9712),l=(u.alphasort,u.alphasorti,u.setopts),f=u.ownProp,p=u.childrenIgnored,h=u.isIgnored;function d(e,t){if("function"==typeof t||3===arguments.length)throw new TypeError("callback provided to sync glob\nSee: https://github.com/isaacs/node-glob/issues/167");return new m(e,t).found}function m(e,t){if(!e)throw new Error("must provide pattern");if("function"==typeof t||3===arguments.length)throw new TypeError("callback provided to sync glob\nSee: https://github.com/isaacs/node-glob/issues/167");if(!(this instanceof m))return new m(e,t);if(l(this,e,t),this.noprocess)return this;var r=this.minimatch.set.length;this.matches=new Array(r);for(var n=0;n<r;n++)this._process(this.minimatch.set[n],n,!1);this._finish()}m.prototype._finish=function(){if(a(this instanceof m),this.realpath){var e=this;this.matches.forEach((function(t,r){var n=e.matches[r]=Object.create(null);for(var i in t)try{i=e._makeAbs(i),n[o.realpathSync(i,e.realpathCache)]=!0}catch(t){if("stat"!==t.syscall)throw t;n[e._makeAbs(i)]=!0}}))}u.finish(this)},m.prototype._process=function(e,t,r){a(this instanceof m);for(var n,o=0;"string"==typeof e[o];)o++;switch(o){case e.length:return void this._processSimple(e.join("/"),t);case 0:n=null;break;default:n=e.slice(0,o).join("/")}var s,u=e.slice(o);null===n?s=".":c(n)||c(e.join("/"))?(n&&c(n)||(n="/"+n),s=n):s=n;var l=this._makeAbs(s);p(this,s)||(u[0]===i.GLOBSTAR?this._processGlobStar(n,s,l,u,t,r):this._processReaddir(n,s,l,u,t,r))},m.prototype._processReaddir=function(e,t,r,n,o,i){var a=this._readdir(r,i);if(a){for(var c=n[0],u=!!this.minimatch.negate,l=c._glob,f=this.dot||"."===l.charAt(0),p=[],h=0;h<a.length;h++)("."!==(g=a[h]).charAt(0)||f)&&(u&&!e?!g.match(c):g.match(c))&&p.push(g);var d=p.length;if(0!==d)if(1!==n.length||this.mark||this.stat)for(n.shift(),h=0;h<d;h++){var m;g=p[h],m=e?[e,g]:[g],this._process(m.concat(n),o,i)}else{this.matches[o]||(this.matches[o]=Object.create(null));for(h=0;h<d;h++){var g=p[h];e&&(g="/"!==e.slice(-1)?e+"/"+g:e+g),"/"!==g.charAt(0)||this.nomount||(g=s.join(this.root,g)),this._emitMatch(o,g)}}}},m.prototype._emitMatch=function(e,t){if(!h(this,t)){var r=this._makeAbs(t);if(this.mark&&(t=this._mark(t)),this.absolute&&(t=r),!this.matches[e][t]){if(this.nodir){var n=this.cache[r];if("DIR"===n||Array.isArray(n))return}this.matches[e][t]=!0,this.stat&&this._stat(t)}}},m.prototype._readdirInGlobStar=function(e){if(this.follow)return this._readdir(e,!1);var t,r;try{r=n.lstatSync(e)}catch(e){if("ENOENT"===e.code)return null}var o=r&&r.isSymbolicLink();return this.symlinks[e]=o,o||!r||r.isDirectory()?t=this._readdir(e,!1):this.cache[e]="FILE",t},m.prototype._readdir=function(e,t){if(t&&!f(this.symlinks,e))return this._readdirInGlobStar(e);if(f(this.cache,e)){var r=this.cache[e];if(!r||"FILE"===r)return null;if(Array.isArray(r))return r}try{return this._readdirEntries(e,n.readdirSync(e))}catch(t){return this._readdirError(e,t),null}},m.prototype._readdirEntries=function(e,t){if(!this.mark&&!this.stat)for(var r=0;r<t.length;r++){var n=t[r];n="/"===e?e+n:e+"/"+n,this.cache[n]=!0}return this.cache[e]=t,t},m.prototype._readdirError=function(e,t){switch(t.code){case"ENOTSUP":case"ENOTDIR":var r=this._makeAbs(e);if(this.cache[r]="FILE",r===this.cwdAbs){var n=new Error(t.code+" invalid cwd "+this.cwd);throw n.path=this.cwd,n.code=t.code,n}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=!1;break;default:if(this.cache[this._makeAbs(e)]=!1,this.strict)throw t;this.silent||console.error("glob error",t)}},m.prototype._processGlobStar=function(e,t,r,n,o,i){var s=this._readdir(r,i);if(s){var a=n.slice(1),c=e?[e]:[],u=c.concat(a);this._process(u,o,!1);var l=s.length;if(!this.symlinks[r]||!i)for(var f=0;f<l;f++)if("."!==s[f].charAt(0)||this.dot){var p=c.concat(s[f],a);this._process(p,o,!0);var h=c.concat(s[f],n);this._process(h,o,!0)}}},m.prototype._processSimple=function(e,t){var r=this._stat(e);if(this.matches[t]||(this.matches[t]=Object.create(null)),r){if(e&&c(e)&&!this.nomount){var n=/[\/\\]$/.test(e);"/"===e.charAt(0)?e=s.join(this.root,e):(e=s.resolve(this.root,e),n&&(e+="/"))}"win32"===process.platform&&(e=e.replace(/\\/g,"/")),this._emitMatch(t,e)}},m.prototype._stat=function(e){var t=this._makeAbs(e),r="/"===e.slice(-1);if(e.length>this.maxLength)return!1;if(!this.stat&&f(this.cache,t)){var o=this.cache[t];if(Array.isArray(o)&&(o="DIR"),!r||"DIR"===o)return o;if(r&&"FILE"===o)return!1}var i=this.statCache[t];if(!i){var s;try{s=n.lstatSync(t)}catch(e){if(e&&("ENOENT"===e.code||"ENOTDIR"===e.code))return this.statCache[t]=!1,!1}if(s&&s.isSymbolicLink())try{i=n.statSync(t)}catch(e){i=s}else i=s}return this.statCache[t]=i,o=!0,i&&(o=i.isDirectory()?"DIR":"FILE"),this.cache[t]=this.cache[t]||o,(!r||"FILE"!==o)&&o},m.prototype._mark=function(e){return u.mark(this,e)},m.prototype._makeAbs=function(e){return u.makeAbs(this,e)}},363:(e,t,r)=>{var n=r(2680),o=Object.create(null),i=r(4640);e.exports=n((function(e,t){return o[e]?(o[e].push(t),null):(o[e]=[t],function(e){return i((function t(){var r=o[e],n=r.length,i=function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n]=e[n];return r}(arguments);try{for(var s=0;s<n;s++)r[s].apply(null,i)}finally{r.length>n?(r.splice(0,n),process.nextTick((function(){t.apply(null,i)}))):delete o[e]}}))}(e))}))},3352:(e,t,r)=>{try{var n=r(970);if("function"!=typeof n.inherits)throw"";e.exports=n.inherits}catch(t){e.exports=r(8248)}},8248:e=>{"function"==typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},1392:e=>{"use strict";var t=e.exports=function(e){return null!==e&&"object"==typeof e&&"function"==typeof e.pipe};t.writable=function(e){return t(e)&&!1!==e.writable&&"function"==typeof e._write&&"object"==typeof e._writableState},t.readable=function(e){return t(e)&&!1!==e.readable&&"function"==typeof e._read&&"object"==typeof e._readableState},t.duplex=function(e){return t.writable(e)&&t.readable(e)},t.transform=function(e){return t.duplex(e)&&"function"==typeof e._transform&&"object"==typeof e._transformState}},9268:(e,t,r)=>{var n;function o(e,t,r){if("function"==typeof t&&(r=t,t={}),!r){if("function"!=typeof Promise)throw new TypeError("callback not provided");return new Promise((function(r,n){o(e,t||{},(function(e,t){e?n(e):r(t)}))}))}n(e,t||{},(function(e,n){e&&("EACCES"===e.code||t&&t.ignoreErrors)&&(e=null,n=!1),r(e,n)}))}r(2058),n="win32"===process.platform||global.TESTING_WINDOWS?r(8340):r(8692),e.exports=o,o.sync=function(e,t){try{return n.sync(e,t||{})}catch(e){if(t&&t.ignoreErrors||"EACCES"===e.code)return!1;throw e}}},8692:(e,t,r)=>{e.exports=o,o.sync=function(e,t){return i(n.statSync(e),t)};var n=r(2058);function o(e,t,r){n.stat(e,(function(e,n){r(e,!e&&i(n,t))}))}function i(e,t){return e.isFile()&&function(e,t){var r=e.mode,n=e.uid,o=e.gid,i=void 0!==t.uid?t.uid:process.getuid&&process.getuid(),s=void 0!==t.gid?t.gid:process.getgid&&process.getgid(),a=parseInt("100",8),c=parseInt("010",8);return r&parseInt("001",8)||r&c&&o===s||r&a&&n===i||r&(a|c)&&0===i}(e,t)}},8340:(e,t,r)=>{e.exports=i,i.sync=function(e,t){return o(n.statSync(e),e,t)};var n=r(2058);function o(e,t,r){return!(!e.isSymbolicLink()&&!e.isFile())&&function(e,t){var r=void 0!==t.pathExt?t.pathExt:process.env.PATHEXT;if(!r)return!0;if(-1!==(r=r.split(";")).indexOf(""))return!0;for(var n=0;n<r.length;n++){var o=r[n].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o)return!0}return!1}(t,r)}function i(e,t,r){n.stat(e,(function(n,i){r(n,!n&&o(i,e,t))}))}},2941:(e,t,r)=>{"use strict";const n=r(8558),o=new Map([[18,"Mojave"],[17,"High Sierra"],[16,"Sierra"],[15,"El Capitan"],[14,"Yosemite"],[13,"Mavericks"],[12,"Mountain Lion"],[11,"Lion"],[10,"Snow Leopard"],[9,"Leopard"],[8,"Tiger"],[7,"Panther"],[6,"Jaguar"],[5,"Puma"]]),i=e=>(e=Number((e||n.release()).split(".")[0]),{name:o.get(e),version:"10."+(e-4)});e.exports=i,e.exports.default=i},836:(e,t,r)=>{e.exports=p,p.Minimatch=h;var n={sep:"/"};try{n=r(7072)}catch(e){}var o=p.GLOBSTAR=h.GLOBSTAR={},i=r(1620),s={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},a="[^/]",c=a+"*?",u="().*{}+?[]^$\\!".split("").reduce((function(e,t){return e[t]=!0,e}),{}),l=/\/+/;function f(e,t){e=e||{},t=t||{};var r={};return Object.keys(t).forEach((function(e){r[e]=t[e]})),Object.keys(e).forEach((function(t){r[t]=e[t]})),r}function p(e,t,r){if("string"!=typeof t)throw new TypeError("glob pattern string required");return r||(r={}),!(!r.nocomment&&"#"===t.charAt(0))&&(""===t.trim()?""===e:new h(t,r).match(e))}function h(e,t){if(!(this instanceof h))return new h(e,t);if("string"!=typeof e)throw new TypeError("glob pattern string required");t||(t={}),e=e.trim(),"/"!==n.sep&&(e=e.split(n.sep).join("/")),this.options=t,this.set=[],this.pattern=e,this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.make()}function d(e,t){if(t||(t=this instanceof h?this.options:{}),void 0===(e=void 0===e?this.pattern:e))throw new TypeError("undefined pattern");return t.nobrace||!e.match(/\{.*\}/)?[e]:i(e)}p.filter=function(e,t){return t=t||{},function(r,n,o){return p(r,e,t)}},p.defaults=function(e){if(!e||!Object.keys(e).length)return p;var t=p,r=function(r,n,o){return t.minimatch(r,n,f(e,o))};return r.Minimatch=function(r,n){return new t.Minimatch(r,f(e,n))},r},h.defaults=function(e){return e&&Object.keys(e).length?p.defaults(e).Minimatch:h},h.prototype.debug=function(){},h.prototype.make=function(){if(!this._made){var e=this.pattern,t=this.options;if(t.nocomment||"#"!==e.charAt(0))if(e){this.parseNegate();var r=this.globSet=this.braceExpand();t.debug&&(this.debug=console.error),this.debug(this.pattern,r),r=this.globParts=r.map((function(e){return e.split(l)})),this.debug(this.pattern,r),r=r.map((function(e,t,r){return e.map(this.parse,this)}),this),this.debug(this.pattern,r),r=r.filter((function(e){return-1===e.indexOf(!1)})),this.debug(this.pattern,r),this.set=r}else this.empty=!0;else this.comment=!0}},h.prototype.parseNegate=function(){var e=this.pattern,t=!1,r=0;if(!this.options.nonegate){for(var n=0,o=e.length;n<o&&"!"===e.charAt(n);n++)t=!t,r++;r&&(this.pattern=e.substr(r)),this.negate=t}},p.braceExpand=function(e,t){return d(e,t)},h.prototype.braceExpand=d,h.prototype.parse=function(e,t){if(e.length>65536)throw new TypeError("pattern is too long");var r=this.options;if(!r.noglobstar&&"**"===e)return o;if(""===e)return"";var n,i="",l=!!r.nocase,f=!1,p=[],h=[],d=!1,g=-1,v=-1,y="."===e.charAt(0)?"":r.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",b=this;function w(){if(n){switch(n){case"*":i+=c,l=!0;break;case"?":i+=a,l=!0;break;default:i+="\\"+n}b.debug("clearStateChar %j %j",n,i),n=!1}}for(var x,S=0,P=e.length;S<P&&(x=e.charAt(S));S++)if(this.debug("%s\t%s %s %j",e,S,i,x),f&&u[x])i+="\\"+x,f=!1;else switch(x){case"/":return!1;case"\\":w(),f=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s\t%s %s %j <-- stateChar",e,S,i,x),d){this.debug("  in class"),"!"===x&&S===v+1&&(x="^"),i+=x;continue}b.debug("call clearStateChar %j",n),w(),n=x,r.noext&&w();continue;case"(":if(d){i+="(";continue}if(!n){i+="\\(";continue}p.push({type:n,start:S-1,reStart:i.length,open:s[n].open,close:s[n].close}),i+="!"===n?"(?:(?!(?:":"(?:",this.debug("plType %j %j",n,i),n=!1;continue;case")":if(d||!p.length){i+="\\)";continue}w(),l=!0;var O=p.pop();i+=O.close,"!"===O.type&&h.push(O),O.reEnd=i.length;continue;case"|":if(d||!p.length||f){i+="\\|",f=!1;continue}w(),i+="|";continue;case"[":if(w(),d){i+="\\"+x;continue}d=!0,v=S,g=i.length,i+=x;continue;case"]":if(S===v+1||!d){i+="\\"+x,f=!1;continue}if(d){var I=e.substring(v+1,S);try{RegExp("["+I+"]")}catch(e){var E=this.parse(I,m);i=i.substr(0,g)+"\\["+E[0]+"\\]",l=l||E[1],d=!1;continue}}l=!0,d=!1,i+=x;continue;default:w(),f?f=!1:!u[x]||"^"===x&&d||(i+="\\"),i+=x}for(d&&(I=e.substr(v+1),E=this.parse(I,m),i=i.substr(0,g)+"\\["+E[0],l=l||E[1]),O=p.pop();O;O=p.pop()){var j=i.slice(O.reStart+O.open.length);this.debug("setting tail",i,O),j=j.replace(/((?:\\{2}){0,64})(\\?)\|/g,(function(e,t,r){return r||(r="\\"),t+t+r+"|"})),this.debug("tail=%j\n   %s",j,j,O,i);var _="*"===O.type?c:"?"===O.type?a:"\\"+O.type;l=!0,i=i.slice(0,O.reStart)+_+"\\("+j}w(),f&&(i+="\\\\");var A=!1;switch(i.charAt(0)){case".":case"[":case"(":A=!0}for(var k=h.length-1;k>-1;k--){var N=h[k],F=i.slice(0,N.reStart),C=i.slice(N.reStart,N.reEnd-8),M=i.slice(N.reEnd-8,N.reEnd),V=i.slice(N.reEnd);M+=V;var T=F.split("(").length-1,D=V;for(S=0;S<T;S++)D=D.replace(/\)[+*?]?/,"");var L="";""===(V=D)&&t!==m&&(L="$"),i=F+C+V+L+M}if(""!==i&&l&&(i="(?=.)"+i),A&&(i=y+i),t===m)return[i,l];if(!l)return e.replace(/\\(.)/g,"$1");var $=r.nocase?"i":"";try{var B=new RegExp("^"+i+"$",$)}catch(e){return new RegExp("$.")}return B._glob=e,B._src=i,B};var m={};p.makeRe=function(e,t){return new h(e,t||{}).makeRe()},h.prototype.makeRe=function(){if(this.regexp||!1===this.regexp)return this.regexp;var e=this.set;if(!e.length)return this.regexp=!1,this.regexp;var t=this.options,r=t.noglobstar?c:t.dot?"(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?":"(?:(?!(?:\\/|^)\\.).)*?",n=t.nocase?"i":"",i=e.map((function(e){return e.map((function(e){return e===o?r:"string"==typeof e?e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"):e._src})).join("\\/")})).join("|");i="^(?:"+i+")$",this.negate&&(i="^(?!"+i+").*$");try{this.regexp=new RegExp(i,n)}catch(e){this.regexp=!1}return this.regexp},p.match=function(e,t,r){var n=new h(t,r=r||{});return e=e.filter((function(e){return n.match(e)})),n.options.nonull&&!e.length&&e.push(t),e},h.prototype.match=function(e,t){if(this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return""===e;if("/"===e&&t)return!0;var r=this.options;"/"!==n.sep&&(e=e.split(n.sep).join("/")),e=e.split(l),this.debug(this.pattern,"split",e);var o,i,s=this.set;for(this.debug(this.pattern,"set",s),i=e.length-1;i>=0&&!(o=e[i]);i--);for(i=0;i<s.length;i++){var a=s[i],c=e;if(r.matchBase&&1===a.length&&(c=[o]),this.matchOne(c,a,t))return!!r.flipNegate||!this.negate}return!r.flipNegate&&this.negate},h.prototype.matchOne=function(e,t,r){var n=this.options;this.debug("matchOne",{this:this,file:e,pattern:t}),this.debug("matchOne",e.length,t.length);for(var i=0,s=0,a=e.length,c=t.length;i<a&&s<c;i++,s++){this.debug("matchOne loop");var u,l=t[s],f=e[i];if(this.debug(t,l,f),!1===l)return!1;if(l===o){this.debug("GLOBSTAR",[t,l,f]);var p=i,h=s+1;if(h===c){for(this.debug("** at the end");i<a;i++)if("."===e[i]||".."===e[i]||!n.dot&&"."===e[i].charAt(0))return!1;return!0}for(;p<a;){var d=e[p];if(this.debug("\nglobstar while",e,p,t,h,d),this.matchOne(e.slice(p),t.slice(h),r))return this.debug("globstar found match!",p,a,d),!0;if("."===d||".."===d||!n.dot&&"."===d.charAt(0)){this.debug("dot detected!",e,p,t,h);break}this.debug("globstar swallow a segment, and continue"),p++}return!(!r||(this.debug("\n>>> no match, partial?",e,p,t,h),p!==a))}if("string"==typeof l?(u=n.nocase?f.toLowerCase()===l.toLowerCase():f===l,this.debug("string match",l,f,u)):(u=f.match(l),this.debug("pattern match",l,f,u)),!u)return!1}if(i===a&&s===c)return!0;if(i===a)return r;if(s===c)return i===a-1&&""===e[i];throw new Error("wtf?")}},9480:e=>{"use strict";e.exports=function(e){try{return e()}catch(e){}}},9584:(e,t,r)=>{"use strict";const n=r(7072),o=r(3032);e.exports=e=>{let t;e=Object.assign({cwd:process.cwd(),path:process.env[o()]},e);let r=n.resolve(e.cwd);const i=[];for(;t!==r;)i.push(n.join(r,"node_modules/.bin")),t=r,r=n.resolve(r,"..");return i.push(n.dirname(process.execPath)),i.concat(e.path).join(n.delimiter)},e.exports.env=t=>{t=Object.assign({env:process.env},t);const r=Object.assign({},t.env),n=o({env:r});return t.path=r[n],r[n]=e.exports(t),r}},4640:(e,t,r)=>{var n=r(2680);function o(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function i(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||"Function wrapped with `once`";return t.onceError=r+" shouldn't be called more than once",t.called=!1,t}e.exports=n(o),e.exports.strict=n(i),o.proto=o((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return o(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return i(this)},configurable:!0})}))},2288:(e,t,r)=>{"use strict";const n=r(8558),o=r(2941),i=r(6468);e.exports=(e,t)=>{if(!e&&t)throw new Error("You can't specify a `release` without specifying `platform`");let r;if("darwin"===(e=e||n.platform())){t||"darwin"!==n.platform()||(t=n.release());const e=t?Number(t.split(".")[0])>15?"macOS":"OS X":"macOS";return r=t?o(t).name:"",e+(r?" "+r:"")}return"linux"===e?(t||"linux"!==n.platform()||(t=n.release()),r=t?t.replace(/^(\d+\.\d+).*/,"$1"):"","Linux"+(r?" "+r:"")):"win32"===e?(t||"win32"!==n.platform()||(t=n.release()),r=t?i(t):"","Windows"+(r?" "+r:"")):e}},1548:e=>{"use strict";e.exports=(e,t)=>(t=t||(()=>{}),e.then((e=>new Promise((e=>{e(t())})).then((()=>e))),(e=>new Promise((e=>{e(t())})).then((()=>{throw e})))))},4832:e=>{"use strict";function t(e){return"/"===e.charAt(0)}function r(e){var t=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/.exec(e),r=t[1]||"",n=Boolean(r&&":"!==r.charAt(1));return Boolean(t[2]||n)}e.exports="win32"===process.platform?r:t,e.exports.posix=t,e.exports.win32=r},3032:e=>{"use strict";e.exports=e=>{const t=(e=e||{}).env||process.env;return"win32"!==(e.platform||process.platform)?"PATH":Object.keys(t).find((e=>"PATH"===e.toUpperCase()))||"Path"}},8984:(e,t,r)=>{var n=r(4640),o=r(9988),i=r(2058),s=function(){},a=/^v?\.0/.test(process.version),c=function(e){return"function"==typeof e},u=function(e){e()},l=function(e,t){return e.pipe(t)};e.exports=function(){var e,t=Array.prototype.slice.call(arguments),r=c(t[t.length-1]||s)&&t.pop()||s;if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new Error("pump requires two streams per minimum");var f=t.map((function(l,p){var h=p<t.length-1;return function(e,t,r,u){u=n(u);var l=!1;e.on("close",(function(){l=!0})),o(e,{readable:t,writable:r},(function(e){if(e)return u(e);l=!0,u()}));var f=!1;return function(t){if(!l&&!f)return f=!0,function(e){return!!a&&!!i&&(e instanceof(i.ReadStream||s)||e instanceof(i.WriteStream||s))&&c(e.close)}(e)?e.close(s):function(e){return e.setHeader&&c(e.abort)}(e)?e.abort():c(e.destroy)?e.destroy():void u(t||new Error("stream was destroyed"))}}(l,h,p>0,(function(t){e||(e=t),t&&f.forEach(u),h||(f.forEach(u),r(e))}))}));return t.reduce(l)}},1716:(e,t)=>{var r;t=e.exports=Y,r="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?function(){var e=Array.prototype.slice.call(arguments,0);e.unshift("SEMVER"),console.log.apply(console,e)}:function(){},t.SEMVER_SPEC_VERSION="2.0.0";var n=256,o=Number.MAX_SAFE_INTEGER||9007199254740991,i=t.re=[],s=t.src=[],a=0,c=a++;s[c]="0|[1-9]\\d*";var u=a++;s[u]="[0-9]+";var l=a++;s[l]="\\d*[a-zA-Z-][a-zA-Z0-9-]*";var f=a++;s[f]="("+s[c]+")\\.("+s[c]+")\\.("+s[c]+")";var p=a++;s[p]="("+s[u]+")\\.("+s[u]+")\\.("+s[u]+")";var h=a++;s[h]="(?:"+s[c]+"|"+s[l]+")";var d=a++;s[d]="(?:"+s[u]+"|"+s[l]+")";var m=a++;s[m]="(?:-("+s[h]+"(?:\\."+s[h]+")*))";var g=a++;s[g]="(?:-?("+s[d]+"(?:\\."+s[d]+")*))";var v=a++;s[v]="[0-9A-Za-z-]+";var y=a++;s[y]="(?:\\+("+s[v]+"(?:\\."+s[v]+")*))";var b=a++,w="v?"+s[f]+s[m]+"?"+s[y]+"?";s[b]="^"+w+"$";var x="[v=\\s]*"+s[p]+s[g]+"?"+s[y]+"?",S=a++;s[S]="^"+x+"$";var P=a++;s[P]="((?:<|>)?=?)";var O=a++;s[O]=s[u]+"|x|X|\\*";var I=a++;s[I]=s[c]+"|x|X|\\*";var E=a++;s[E]="[v=\\s]*("+s[I]+")(?:\\.("+s[I]+")(?:\\.("+s[I]+")(?:"+s[m]+")?"+s[y]+"?)?)?";var j=a++;s[j]="[v=\\s]*("+s[O]+")(?:\\.("+s[O]+")(?:\\.("+s[O]+")(?:"+s[g]+")?"+s[y]+"?)?)?";var _=a++;s[_]="^"+s[P]+"\\s*"+s[E]+"$";var A=a++;s[A]="^"+s[P]+"\\s*"+s[j]+"$";var k=a++;s[k]="(?:^|[^\\d])(\\d{1,16})(?:\\.(\\d{1,16}))?(?:\\.(\\d{1,16}))?(?:$|[^\\d])";var N=a++;s[N]="(?:~>?)";var F=a++;s[F]="(\\s*)"+s[N]+"\\s+",i[F]=new RegExp(s[F],"g");var C=a++;s[C]="^"+s[N]+s[E]+"$";var M=a++;s[M]="^"+s[N]+s[j]+"$";var V=a++;s[V]="(?:\\^)";var T=a++;s[T]="(\\s*)"+s[V]+"\\s+",i[T]=new RegExp(s[T],"g");var D=a++;s[D]="^"+s[V]+s[E]+"$";var L=a++;s[L]="^"+s[V]+s[j]+"$";var $=a++;s[$]="^"+s[P]+"\\s*("+x+")$|^$";var B=a++;s[B]="^"+s[P]+"\\s*("+w+")$|^$";var R=a++;s[R]="(\\s*)"+s[P]+"\\s*("+x+"|"+s[E]+")",i[R]=new RegExp(s[R],"g");var G=a++;s[G]="^\\s*("+s[E]+")\\s+-\\s+("+s[E]+")\\s*$";var W=a++;s[W]="^\\s*("+s[j]+")\\s+-\\s+("+s[j]+")\\s*$";var U=a++;s[U]="(<|>)?=?\\s*\\*";for(var K=0;K<35;K++)r(K,s[K]),i[K]||(i[K]=new RegExp(s[K]));function q(e,t){if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),e instanceof Y)return e;if("string"!=typeof e)return null;if(e.length>n)return null;if(!(t.loose?i[S]:i[b]).test(e))return null;try{return new Y(e,t)}catch(e){return null}}function Y(e,t){if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),e instanceof Y){if(e.loose===t.loose)return e;e=e.version}else if("string"!=typeof e)throw new TypeError("Invalid Version: "+e);if(e.length>n)throw new TypeError("version is longer than "+n+" characters");if(!(this instanceof Y))return new Y(e,t);r("SemVer",e,t),this.options=t,this.loose=!!t.loose;var s=e.trim().match(t.loose?i[S]:i[b]);if(!s)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+s[1],this.minor=+s[2],this.patch=+s[3],this.major>o||this.major<0)throw new TypeError("Invalid major version");if(this.minor>o||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>o||this.patch<0)throw new TypeError("Invalid patch version");s[4]?this.prerelease=s[4].split(".").map((function(e){if(/^[0-9]+$/.test(e)){var t=+e;if(t>=0&&t<o)return t}return e})):this.prerelease=[],this.build=s[5]?s[5].split("."):[],this.format()}t.parse=q,t.valid=function(e,t){var r=q(e,t);return r?r.version:null},t.clean=function(e,t){var r=q(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null},t.SemVer=Y,Y.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version},Y.prototype.toString=function(){return this.version},Y.prototype.compare=function(e){return r("SemVer.compare",this.version,this.options,e),e instanceof Y||(e=new Y(e,this.options)),this.compareMain(e)||this.comparePre(e)},Y.prototype.compareMain=function(e){return e instanceof Y||(e=new Y(e,this.options)),J(this.major,e.major)||J(this.minor,e.minor)||J(this.patch,e.patch)},Y.prototype.comparePre=function(e){if(e instanceof Y||(e=new Y(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;var t=0;do{var n=this.prerelease[t],o=e.prerelease[t];if(r("prerelease compare",t,n,o),void 0===n&&void 0===o)return 0;if(void 0===o)return 1;if(void 0===n)return-1;if(n!==o)return J(n,o)}while(++t)},Y.prototype.inc=function(e,t){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t),this.inc("pre",t);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t),this.inc("pre",t);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{for(var r=this.prerelease.length;--r>=0;)"number"==typeof this.prerelease[r]&&(this.prerelease[r]++,r=-2);-1===r&&this.prerelease.push(0)}t&&(this.prerelease[0]===t?isNaN(this.prerelease[1])&&(this.prerelease=[t,0]):this.prerelease=[t,0]);break;default:throw new Error("invalid increment argument: "+e)}return this.format(),this.raw=this.version,this},t.inc=function(e,t,r,n){"string"==typeof r&&(n=r,r=void 0);try{return new Y(e,r).inc(t,n).version}catch(e){return null}},t.diff=function(e,t){if(Z(e,t))return null;var r=q(e),n=q(t);if(r.prerelease.length||n.prerelease.length){for(var o in r)if(("major"===o||"minor"===o||"patch"===o)&&r[o]!==n[o])return"pre"+o;return"prerelease"}for(var o in r)if(("major"===o||"minor"===o||"patch"===o)&&r[o]!==n[o])return o},t.compareIdentifiers=J;var H=/^[0-9]+$/;function J(e,t){var r=H.test(e),n=H.test(t);return r&&n&&(e=+e,t=+t),r&&!n?-1:n&&!r?1:e<t?-1:e>t?1:0}function z(e,t,r){return new Y(e,r).compare(new Y(t,r))}function Q(e,t,r){return z(e,t,r)>0}function X(e,t,r){return z(e,t,r)<0}function Z(e,t,r){return 0===z(e,t,r)}function ee(e,t,r){return 0!==z(e,t,r)}function te(e,t,r){return z(e,t,r)>=0}function re(e,t,r){return z(e,t,r)<=0}function ne(e,t,r,n){var o;switch(t){case"===":"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),o=e===r;break;case"!==":"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),o=e!==r;break;case"":case"=":case"==":o=Z(e,r,n);break;case"!=":o=ee(e,r,n);break;case">":o=Q(e,r,n);break;case">=":o=te(e,r,n);break;case"<":o=X(e,r,n);break;case"<=":o=re(e,r,n);break;default:throw new TypeError("Invalid operator: "+t)}return o}function oe(e,t){if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),e instanceof oe){if(e.loose===!!t.loose)return e;e=e.value}if(!(this instanceof oe))return new oe(e,t);r("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===ie?this.value="":this.value=this.operator+this.semver.version,r("comp",this)}t.rcompareIdentifiers=function(e,t){return J(t,e)},t.major=function(e,t){return new Y(e,t).major},t.minor=function(e,t){return new Y(e,t).minor},t.patch=function(e,t){return new Y(e,t).patch},t.compare=z,t.compareLoose=function(e,t){return z(e,t,!0)},t.rcompare=function(e,t,r){return z(t,e,r)},t.sort=function(e,r){return e.sort((function(e,n){return t.compare(e,n,r)}))},t.rsort=function(e,r){return e.sort((function(e,n){return t.rcompare(e,n,r)}))},t.gt=Q,t.lt=X,t.eq=Z,t.neq=ee,t.gte=te,t.lte=re,t.cmp=ne,t.Comparator=oe;var ie={};function se(e,t){if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),e instanceof se)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new se(e.raw,t);if(e instanceof oe)return new se(e.value,t);if(!(this instanceof se))return new se(e,t);if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e,this.set=e.split(/\s*\|\|\s*/).map((function(e){return this.parseRange(e.trim())}),this).filter((function(e){return e.length})),!this.set.length)throw new TypeError("Invalid SemVer Range: "+e);this.format()}function ae(e){return!e||"x"===e.toLowerCase()||"*"===e}function ce(e,t,r,n,o,i,s,a,c,u,l,f,p){return((t=ae(r)?"":ae(n)?">="+r+".0.0":ae(o)?">="+r+"."+n+".0":">="+t)+" "+(a=ae(c)?"":ae(u)?"<"+(+c+1)+".0.0":ae(l)?"<"+c+"."+(+u+1)+".0":f?"<="+c+"."+u+"."+l+"-"+f:"<="+a)).trim()}function ue(e,t,n){for(var o=0;o<e.length;o++)if(!e[o].test(t))return!1;if(n||(n={}),t.prerelease.length&&!n.includePrerelease){for(o=0;o<e.length;o++)if(r(e[o].semver),e[o].semver!==ie&&e[o].semver.prerelease.length>0){var i=e[o].semver;if(i.major===t.major&&i.minor===t.minor&&i.patch===t.patch)return!0}return!1}return!0}function le(e,t,r){try{t=new se(t,r)}catch(e){return!1}return t.test(e)}function fe(e,t,r,n){var o,i,s,a,c;switch(e=new Y(e,n),t=new se(t,n),r){case">":o=Q,i=re,s=X,a=">",c=">=";break;case"<":o=X,i=te,s=Q,a="<",c="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(le(e,t,n))return!1;for(var u=0;u<t.set.length;++u){var l=t.set[u],f=null,p=null;if(l.forEach((function(e){e.semver===ie&&(e=new oe(">=0.0.0")),f=f||e,p=p||e,o(e.semver,f.semver,n)?f=e:s(e.semver,p.semver,n)&&(p=e)})),f.operator===a||f.operator===c)return!1;if((!p.operator||p.operator===a)&&i(e,p.semver))return!1;if(p.operator===c&&s(e,p.semver))return!1}return!0}oe.prototype.parse=function(e){var t=this.options.loose?i[$]:i[B],r=e.match(t);if(!r)throw new TypeError("Invalid comparator: "+e);this.operator=r[1],"="===this.operator&&(this.operator=""),r[2]?this.semver=new Y(r[2],this.options.loose):this.semver=ie},oe.prototype.toString=function(){return this.value},oe.prototype.test=function(e){return r("Comparator.test",e,this.options.loose),this.semver===ie||("string"==typeof e&&(e=new Y(e,this.options)),ne(e,this.operator,this.semver,this.options))},oe.prototype.intersects=function(e,t){if(!(e instanceof oe))throw new TypeError("a Comparator is required");var r;if(t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1}),""===this.operator)return r=new se(e.value,t),le(this.value,r,t);if(""===e.operator)return r=new se(this.value,t),le(e.semver,r,t);var n=!(">="!==this.operator&&">"!==this.operator||">="!==e.operator&&">"!==e.operator),o=!("<="!==this.operator&&"<"!==this.operator||"<="!==e.operator&&"<"!==e.operator),i=this.semver.version===e.semver.version,s=!(">="!==this.operator&&"<="!==this.operator||">="!==e.operator&&"<="!==e.operator),a=ne(this.semver,"<",e.semver,t)&&(">="===this.operator||">"===this.operator)&&("<="===e.operator||"<"===e.operator),c=ne(this.semver,">",e.semver,t)&&("<="===this.operator||"<"===this.operator)&&(">="===e.operator||">"===e.operator);return n||o||i&&s||a||c},t.Range=se,se.prototype.format=function(){return this.range=this.set.map((function(e){return e.join(" ").trim()})).join("||").trim(),this.range},se.prototype.toString=function(){return this.range},se.prototype.parseRange=function(e){var t=this.options.loose;e=e.trim();var n=t?i[W]:i[G];e=e.replace(n,ce),r("hyphen replace",e),e=e.replace(i[R],"$1$2$3"),r("comparator trim",e,i[R]),e=(e=(e=e.replace(i[F],"$1~")).replace(i[T],"$1^")).split(/\s+/).join(" ");var o=t?i[$]:i[B],s=e.split(" ").map((function(e){return function(e,t){return r("comp",e,t),e=function(e,t){return e.trim().split(/\s+/).map((function(e){return function(e,t){r("caret",e,t),t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1});var n=t.loose?i[L]:i[D];return e.replace(n,(function(t,n,o,i,s){var a;return r("caret",e,t,n,o,i,s),ae(n)?a="":ae(o)?a=">="+n+".0.0 <"+(+n+1)+".0.0":ae(i)?a="0"===n?">="+n+"."+o+".0 <"+n+"."+(+o+1)+".0":">="+n+"."+o+".0 <"+(+n+1)+".0.0":s?(r("replaceCaret pr",s),"-"!==s.charAt(0)&&(s="-"+s),a="0"===n?"0"===o?">="+n+"."+o+"."+i+s+" <"+n+"."+o+"."+(+i+1):">="+n+"."+o+"."+i+s+" <"+n+"."+(+o+1)+".0":">="+n+"."+o+"."+i+s+" <"+(+n+1)+".0.0"):(r("no pr"),a="0"===n?"0"===o?">="+n+"."+o+"."+i+" <"+n+"."+o+"."+(+i+1):">="+n+"."+o+"."+i+" <"+n+"."+(+o+1)+".0":">="+n+"."+o+"."+i+" <"+(+n+1)+".0.0"),r("caret return",a),a}))}(e,t)})).join(" ")}(e,t),r("caret",e),e=function(e,t){return e.trim().split(/\s+/).map((function(e){return function(e,t){t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1});var n=t.loose?i[M]:i[C];return e.replace(n,(function(t,n,o,i,s){var a;return r("tilde",e,t,n,o,i,s),ae(n)?a="":ae(o)?a=">="+n+".0.0 <"+(+n+1)+".0.0":ae(i)?a=">="+n+"."+o+".0 <"+n+"."+(+o+1)+".0":s?(r("replaceTilde pr",s),"-"!==s.charAt(0)&&(s="-"+s),a=">="+n+"."+o+"."+i+s+" <"+n+"."+(+o+1)+".0"):a=">="+n+"."+o+"."+i+" <"+n+"."+(+o+1)+".0",r("tilde return",a),a}))}(e,t)})).join(" ")}(e,t),r("tildes",e),e=function(e,t){return r("replaceXRanges",e,t),e.split(/\s+/).map((function(e){return function(e,t){e=e.trim(),t&&"object"==typeof t||(t={loose:!!t,includePrerelease:!1});var n=t.loose?i[A]:i[_];return e.replace(n,(function(t,n,o,i,s,a){r("xRange",e,t,n,o,i,s,a);var c=ae(o),u=c||ae(i),l=u||ae(s);return"="===n&&l&&(n=""),c?t=">"===n||"<"===n?"<0.0.0":"*":n&&l?(u&&(i=0),l&&(s=0),">"===n?(n=">=",u?(o=+o+1,i=0,s=0):l&&(i=+i+1,s=0)):"<="===n&&(n="<",u?o=+o+1:i=+i+1),t=n+o+"."+i+"."+s):u?t=">="+o+".0.0 <"+(+o+1)+".0.0":l&&(t=">="+o+"."+i+".0 <"+o+"."+(+i+1)+".0"),r("xRange return",t),t}))}(e,t)})).join(" ")}(e,t),r("xrange",e),e=function(e,t){return r("replaceStars",e,t),e.trim().replace(i[U],"")}(e,t),r("stars",e),e}(e,this.options)}),this).join(" ").split(/\s+/);return this.options.loose&&(s=s.filter((function(e){return!!e.match(o)}))),s.map((function(e){return new oe(e,this.options)}),this)},se.prototype.intersects=function(e,t){if(!(e instanceof se))throw new TypeError("a Range is required");return this.set.some((function(r){return r.every((function(r){return e.set.some((function(e){return e.every((function(e){return r.intersects(e,t)}))}))}))}))},t.toComparators=function(e,t){return new se(e,t).set.map((function(e){return e.map((function(e){return e.value})).join(" ").trim().split(" ")}))},se.prototype.test=function(e){if(!e)return!1;"string"==typeof e&&(e=new Y(e,this.options));for(var t=0;t<this.set.length;t++)if(ue(this.set[t],e,this.options))return!0;return!1},t.satisfies=le,t.maxSatisfying=function(e,t,r){var n=null,o=null;try{var i=new se(t,r)}catch(e){return null}return e.forEach((function(e){i.test(e)&&(n&&-1!==o.compare(e)||(o=new Y(n=e,r)))})),n},t.minSatisfying=function(e,t,r){var n=null,o=null;try{var i=new se(t,r)}catch(e){return null}return e.forEach((function(e){i.test(e)&&(n&&1!==o.compare(e)||(o=new Y(n=e,r)))})),n},t.validRange=function(e,t){try{return new se(e,t).range||"*"}catch(e){return null}},t.ltr=function(e,t,r){return fe(e,t,"<",r)},t.gtr=function(e,t,r){return fe(e,t,">",r)},t.outside=fe,t.prerelease=function(e,t){var r=q(e,t);return r&&r.prerelease.length?r.prerelease:null},t.intersects=function(e,t,r){return e=new se(e,r),t=new se(t,r),e.intersects(t)},t.coerce=function(e){if(e instanceof Y)return e;if("string"!=typeof e)return null;var t=e.match(i[k]);return null==t?null:q((t[1]||"0")+"."+(t[2]||"0")+"."+(t[3]||"0"))}},6176:(e,t,r)=>{"use strict";var n=r(184);e.exports=function(e){var t=e.match(n);if(!t)return null;var r=t[0].replace(/#! ?/,"").split(" "),o=r[0].split("/").pop(),i=r[1];return"env"===o?i:o+(i?" "+i:"")}},184:e=>{"use strict";e.exports=/^#!.*/},200:(e,t,r)=>{var n,o=r(6608),i=r(6132),s=r(467);function a(){l&&(l=!1,i.forEach((function(e){try{process.removeListener(e,u[e])}catch(e){}})),process.emit=d,process.reallyExit=p,n.count-=1)}function c(e,t,r){n.emitted[e]||(n.emitted[e]=!0,n.emit(e,t,r))}"function"!=typeof s&&(s=s.EventEmitter),process.__signal_exit_emitter__?n=process.__signal_exit_emitter__:((n=process.__signal_exit_emitter__=new s).count=0,n.emitted={}),n.infinite||(n.setMaxListeners(1/0),n.infinite=!0),e.exports=function(e,t){o.equal(typeof e,"function","a callback must be provided for exit handler"),!1===l&&f();var r="exit";return t&&t.alwaysLast&&(r="afterexit"),n.on(r,e),function(){n.removeListener(r,e),0===n.listeners("exit").length&&0===n.listeners("afterexit").length&&a()}},e.exports.unload=a;var u={};i.forEach((function(e){u[e]=function(){process.listeners(e).length===n.count&&(a(),c("exit",null,e),c("afterexit",null,e),process.kill(process.pid,e))}})),e.exports.signals=function(){return i},e.exports.load=f;var l=!1;function f(){l||(l=!0,n.count+=1,i=i.filter((function(e){try{return process.on(e,u[e]),!0}catch(e){return!1}})),process.emit=m,process.reallyExit=h)}var p=process.reallyExit;function h(e){process.exitCode=e||0,c("exit",process.exitCode,null),c("afterexit",process.exitCode,null),p.call(process,process.exitCode)}var d=process.emit;function m(e,t){if("exit"===e){void 0!==t&&(process.exitCode=t);var r=d.apply(this,arguments);return c("exit",process.exitCode,null),c("afterexit",process.exitCode,null),r}return d.apply(this,arguments)}},6132:e=>{e.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"],"win32"!==process.platform&&e.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT"),"linux"===process.platform&&e.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")},9860:e=>{"use strict";e.exports=function(e){var t="string"==typeof e?"\n":"\n".charCodeAt(),r="string"==typeof e?"\r":"\r".charCodeAt();return e[e.length-1]===t&&(e=e.slice(0,e.length-1)),e[e.length-1]===r&&(e=e.slice(0,e.length-1)),e}},8460:(e,t,r)=>{e.exports=u,u.sync=function(e,t){for(var r=c(e,t=t||{}),n=r.env,i=r.ext,u=r.extExe,l=[],f=0,p=n.length;f<p;f++){var h=n[f];'"'===h.charAt(0)&&'"'===h.slice(-1)&&(h=h.slice(1,-1));var d=o.join(h,e);!h&&/^\.[\\\/]/.test(e)&&(d=e.slice(0,2)+d);for(var m=0,g=i.length;m<g;m++){var v=d+i[m];try{if(s.sync(v,{pathExt:u})){if(!t.all)return v;l.push(v)}}catch(e){}}}if(t.all&&l.length)return l;if(t.nothrow)return null;throw a(e)};var n="win32"===process.platform||"cygwin"===process.env.OSTYPE||"msys"===process.env.OSTYPE,o=r(7072),i=n?";":":",s=r(9268);function a(e){var t=new Error("not found: "+e);return t.code="ENOENT",t}function c(e,t){var r=t.colon||i,o=t.path||process.env.PATH||"",s=[""];o=o.split(r);var a="";return n&&(o.unshift(process.cwd()),s=(a=t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM").split(r),-1!==e.indexOf(".")&&""!==s[0]&&s.unshift("")),(e.match(/\//)||n&&e.match(/\\/))&&(o=[""]),{env:o,ext:s,extExe:a}}function u(e,t,r){"function"==typeof t&&(r=t,t={});var n=c(e,t),i=n.env,u=n.ext,l=n.extExe,f=[];!function n(c,p){if(c===p)return t.all&&f.length?r(null,f):r(a(e));var h=i[c];'"'===h.charAt(0)&&'"'===h.slice(-1)&&(h=h.slice(1,-1));var d=o.join(h,e);!h&&/^\.[\\\/]/.test(e)&&(d=e.slice(0,2)+d),function e(o,i){if(o===i)return n(c+1,p);var a=u[o];s(d+a,{pathExt:l},(function(n,s){if(!n&&s){if(!t.all)return r(null,d+a);f.push(d+a)}return e(o+1,i)}))}(0,u.length)}(0,i.length)}},6468:(e,t,r)=>{"use strict";const n=r(8558),o=r(8636),i=new Map([["10.0","10"],["6.3","8.1"],["6.2","8"],["6.1","7"],["6.0","Vista"],["5.2","Server 2003"],["5.1","XP"],["5.0","2000"],["4.9","ME"],["4.1","98"],["4.0","95"]]);e.exports=e=>{const t=/\d+\.\d/.exec(e||n.release());if(e&&!t)throw new Error("`release` argument doesn't match `n.n`");const r=(t||[])[0];if((!e||e===n.release())&&["6.1","6.2","6.3","10.0"].includes(r)){const e=((o.sync("wmic",["os","get","Caption"]).stdout||"").match(/2008|2012|2016/)||[])[0];if(e)return`Server ${e}`}return i.get(r)}},8636:(e,t,r)=>{"use strict";const n=r(7072),o=r(4368),i=r(7468),s=r(9860),a=r(9584),c=r(1392),u=r(9068),l=r(1548),f=r(200),p=r(7688),h=r(6174);function d(e,t,r){let o;return(r=Object.assign({extendEnv:!0,env:{}},r)).extendEnv&&(r.env=Object.assign({},process.env,r.env)),!0===r.__winShell?(delete r.__winShell,o={command:e,args:t,options:r,file:e,original:{cmd:e,args:t}}):o=i._parse(e,t,r),(r=Object.assign({maxBuffer:1e7,buffer:!0,stripEof:!0,preferLocal:!0,localDir:o.options.cwd||process.cwd(),encoding:"utf8",reject:!0,cleanup:!0},o.options)).stdio=h(r),r.preferLocal&&(r.env=a.env(Object.assign({},r,{cwd:r.localDir}))),r.detached&&(r.cleanup=!1),"win32"===process.platform&&"cmd.exe"===n.basename(o.command)&&o.args.unshift("/q"),{cmd:o.command,args:o.args,opts:r,parsed:o}}function m(e,t){return t&&e.stripEof&&(t=s(t)),t}function g(e,t,r){let n="/bin/sh",o=["-c",t];return r=Object.assign({},r),"win32"===process.platform&&(r.__winShell=!0,n=process.env.comspec||"cmd.exe",o=["/s","/c",`"${t}"`],r.windowsVerbatimArguments=!0),r.shell&&(n=r.shell,delete r.shell),e(n,o,r)}function v(e,t,{encoding:r,buffer:n,maxBuffer:o}){if(!e[t])return null;let i;return i=n?r?u(e[t],{encoding:r,maxBuffer:o}):u.buffer(e[t],{maxBuffer:o}):new Promise(((r,n)=>{e[t].once("end",r).once("error",n)})),i.catch((e=>{throw e.stream=t,e.message=`${t} ${e.message}`,e}))}function y(e,t){const{stdout:r,stderr:n}=e;let o=e.error;const{code:i,signal:s}=e,{parsed:a,joinedCmd:c}=t,u=t.timedOut||!1;if(!o){let e="";Array.isArray(a.opts.stdio)?("inherit"!==a.opts.stdio[2]&&(e+=e.length>0?n:`\n${n}`),"inherit"!==a.opts.stdio[1]&&(e+=`\n${r}`)):"inherit"!==a.opts.stdio&&(e=`\n${n}${r}`),o=new Error(`Command failed: ${c}${e}`),o.code=i<0?p(i):i}return o.stdout=r,o.stderr=n,o.failed=!0,o.signal=s||null,o.cmd=c,o.timedOut=u,o}function b(e,t){let r=e;return Array.isArray(t)&&t.length>0&&(r+=" "+t.join(" ")),r}e.exports=(e,t,r)=>{const n=d(e,t,r),{encoding:s,buffer:a,maxBuffer:u}=n.opts,p=b(e,t);let h,g;try{h=o.spawn(n.cmd,n.args,n.opts)}catch(e){return Promise.reject(e)}n.opts.cleanup&&(g=f((()=>{h.kill()})));let w=null,x=!1;const S=()=>{w&&(clearTimeout(w),w=null),g&&g()};n.opts.timeout>0&&(w=setTimeout((()=>{w=null,x=!0,h.kill(n.opts.killSignal)}),n.opts.timeout));const P=new Promise((e=>{h.on("exit",((t,r)=>{S(),e({code:t,signal:r})})),h.on("error",(t=>{S(),e({error:t})})),h.stdin&&h.stdin.on("error",(t=>{S(),e({error:t})}))}));function O(){h.stdout&&h.stdout.destroy(),h.stderr&&h.stderr.destroy()}const I=()=>l(Promise.all([P,v(h,"stdout",{encoding:s,buffer:a,maxBuffer:u}),v(h,"stderr",{encoding:s,buffer:a,maxBuffer:u})]).then((e=>{const t=e[0];if(t.stdout=e[1],t.stderr=e[2],t.error||0!==t.code||null!==t.signal){const e=y(t,{joinedCmd:p,parsed:n,timedOut:x});if(e.killed=e.killed||h.killed,!n.opts.reject)return e;throw e}return{stdout:m(n.opts,t.stdout),stderr:m(n.opts,t.stderr),code:0,failed:!1,killed:!1,signal:null,cmd:p,timedOut:!1}})),O);return i._enoent.hookChildProcess(h,n.parsed),function(e,t){null!=t&&(c(t)?t.pipe(e.stdin):e.stdin.end(t))}(h,n.opts.input),h.then=(e,t)=>I().then(e,t),h.catch=e=>I().catch(e),h},e.exports.stdout=(...t)=>e.exports(...t).then((e=>e.stdout)),e.exports.stderr=(...t)=>e.exports(...t).then((e=>e.stderr)),e.exports.shell=(t,r)=>g(e.exports,t,r),e.exports.sync=(e,t,r)=>{const n=d(e,t,r),i=b(e,t);if(c(n.opts.input))throw new TypeError("The `input` option cannot be a stream in sync mode");const s=o.spawnSync(n.cmd,n.args,n.opts);if(s.code=s.status,s.error||0!==s.status||null!==s.signal){const e=y(s,{joinedCmd:i,parsed:n});if(!n.opts.reject)return e;throw e}return{stdout:m(n.opts,s.stdout),stderr:m(n.opts,s.stderr),code:0,failed:!1,signal:null,cmd:i,timedOut:!1}},e.exports.shellSync=(t,r)=>g(e.exports.sync,t,r)},7688:(e,t,r)=>{"use strict";const n=r(970);let o;if("function"==typeof n.getSystemErrorName)e.exports=n.getSystemErrorName;else{try{if(o=process.binding("uv"),"function"!=typeof o.errname)throw new TypeError("uv.errname is not a function")}catch(e){console.error("execa/lib/errname: unable to establish process.binding('uv')",e),o=null}e.exports=e=>i(o,e)}function i(e,t){if(e)return e.errname(t);if(!(t<0))throw new Error("err >= 0");return`Unknown system error ${t}`}e.exports.__test__=i},6174:e=>{"use strict";const t=["stdin","stdout","stderr"];e.exports=e=>{if(!e)return null;if(e.stdio&&(e=>t.some((t=>Boolean(e[t]))))(e))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${t.map((e=>`\`${e}\``)).join(", ")}`);if("string"==typeof e.stdio)return e.stdio;const r=e.stdio||[];if(!Array.isArray(r))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof r}\``);const n=[],o=Math.max(r.length,t.length);for(let i=0;i<o;i++){let o=null;void 0!==r[i]?o=r[i]:void 0!==e[t[i]]&&(o=e[t[i]]),n[i]=o}return n}},656:(e,t,r)=>{"use strict";const{PassThrough:n}=r(8378);e.exports=e=>{e=Object.assign({},e);const{array:t}=e;let{encoding:r}=e;const o="buffer"===r;let i=!1;t?i=!(r||o):r=r||"utf8",o&&(r=null);let s=0;const a=[],c=new n({objectMode:i});return r&&c.setEncoding(r),c.on("data",(e=>{a.push(e),i?s=a.length:s+=e.length})),c.getBufferedValue=()=>t?a:o?Buffer.concat(a,s):a.join(""),c.getBufferedLength=()=>s,c}},9068:(e,t,r)=>{"use strict";const n=r(8984),o=r(656);class i extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}}function s(e,t){if(!e)return Promise.reject(new Error("Expected a stream"));t=Object.assign({maxBuffer:1/0},t);const{maxBuffer:r}=t;let s;return new Promise(((a,c)=>{const u=e=>{e&&(e.bufferedData=s.getBufferedValue()),c(e)};s=n(e,o(t),(e=>{e?u(e):a()})),s.on("data",(()=>{s.getBufferedLength()>r&&u(new i)}))})).then((()=>s.getBufferedValue()))}e.exports=s,e.exports.buffer=(e,t)=>s(e,Object.assign({},t,{encoding:"buffer"})),e.exports.array=(e,t)=>s(e,Object.assign({},t,{array:!0})),e.exports.MaxBufferError=i},2680:e=>{e.exports=function e(t,r){if(t&&r)return e(t)(r);if("function"!=typeof t)throw new TypeError("need wrapper function");return Object.keys(t).forEach((function(e){n[e]=t[e]})),n;function n(){for(var e=new Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var n=t.apply(this,e),o=e[e.length-1];return"function"==typeof n&&n!==o&&Object.keys(o).forEach((function(e){n[e]=o[e]})),n}}},4824:e=>{"use strict";function t(e){return e}e.exports={date:t,error:t,symbol:t,string:t,number:t,boolean:t,null:t,undefined:t}},324:(e,t,r)=>{"use strict";var n=r(3648),o=r(4616),i=r(4824);function s(e,t){return void 0===e?t:e}e.exports=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{indent:s(e.indent," "),prefix:s(e.prefix,"\n"),postfix:s(e.postfix,""),errorToString:e.errorToString||n,dateToString:e.dateToString||o,colors:Object.assign({},i,e.colors)}}},4616:e=>{"use strict";e.exports=function(e){return`new Date(${Date.prototype.toISOString.call(e)})`}},3648:e=>{"use strict";e.exports=function(e){return Error.prototype.toString.call(e)}},9411:e=>{"use strict";e.exports=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"  ",r="",n=0;n<e;n+=1)r+=t;return r}},6232:(e,t,r)=>{"use strict";var n=r(7344),o=r(324),i=r(9411),s=["object","array"];e.exports=function(e,t){var r=o(t),a=r.colors,c=r.prefix,u=r.postfix,l=r.dateToString,f=r.errorToString,p=r.indent,h=new Map;function d(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(0===Object.keys(e).length)return" {}";var o="\n",a=i(t,p);return Object.keys(e).forEach((function(c){var u=e[c],l=n(u),f=i(r,"  "),p=-1!==s.indexOf(l)?"":" ",h=v(u)?" [Circular]":g(l,u,t+1,r);o+=`${f}${a}${c}:${p}${h}\n`})),o.substring(0,o.length-1)}function m(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(0===e.length)return" []";var o="\n",s=i(t,p);return e.forEach((function(e){var a=n(e),c=i(r,"  "),u=v(e)?"[Circular]":g(a,e,t,r+1).toString().trimLeft();o+=`${c}${s}- ${u}\n`})),o.substring(0,o.length-1)}function g(e,t,r,n){switch(e){case"array":return m(t,r,n);case"object":return d(t,r,n);case"string":return a.string(t);case"symbol":return a.symbol(t.toString());case"number":return a.number(t);case"boolean":return a.boolean(t);case"null":return a.null("null");case"undefined":return a.undefined("undefined");case"date":return a.date(l(t));case"error":return a.error(f(t));default:return t&&t.toString?t.toString():Object.prototype.toString.call(t)}}function v(e){return!(-1===["object","array"].indexOf(n(e))||!h.has(e)&&(h.set(e),1))}var y="";return h.set(e),"object"===n(e)&&Object.keys(e).length>0?y=d(e):"array"===n(e)&&e.length>0&&(y=m(e)),0===y.length?"":`${c}${y.slice(1)}${u}`}},7344:e=>{"use strict";e.exports=function(e){return Array.isArray(e)?"array":e instanceof Date?"date":e instanceof Error?"error":null===e?"null":"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)?"object":typeof e}},6608:e=>{"use strict";e.exports=require("assert")},4368:e=>{"use strict";e.exports=require("child_process")},467:e=>{"use strict";e.exports=require("events")},2058:e=>{"use strict";e.exports=require("fs")},8558:e=>{"use strict";e.exports=require("os")},7072:e=>{"use strict";e.exports=require("path")},8378:e=>{"use strict";e.exports=require("stream")},970:e=>{"use strict";e.exports=require("util")}},t={},r=function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}(2980);module.exports=r})();