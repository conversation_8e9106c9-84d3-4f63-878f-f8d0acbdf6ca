import React from 'react';
import { MessageList } from '../components/MessageList';

interface ChatModeProps {
    messages: any[];
    isLoading: boolean;
    onSendMessage: (content: string) => void;
}

export const ChatMode: React.FC<ChatModeProps> = ({ messages, isLoading, onSendMessage }) => {
    return (
        <div className="chat-mode">
            <div className="chat-area">
                {messages.length === 0 && !isLoading ? (
                    <div className="empty-state">
                        <div>
                            <h3>Welcome to SIA AI Assistant</h3>
                            <p>Ask me anything about your code, or request help with development tasks.</p>
                            <div style={{ marginTop: '16px', fontSize: '12px', opacity: 0.7 }}>
                                <p>• Ask questions about your codebase</p>
                                <p>• Get explanations for complex code</p>
                                <p>• Request code reviews and suggestions</p>
                                <p>• Search for information online</p>
                            </div>
                        </div>
                    </div>
                ) : (
                    <MessageList messages={messages} isLoading={isLoading} />
                )}
            </div>
        </div>
    );
};
