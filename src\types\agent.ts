// Agent-related types and interfaces

import { AgentState, Plan, PlanStep } from './messaging';

export interface AgentContext {
  currentMode: 'chat' | 'agent' | 'auto-agent';
  currentModel: string;
  conversationHistory: any[];
  workspaceRoot?: string;
  activeFiles: string[];
  recentErrors: string[];
  projectInfo?: ProjectInfo;
}

export interface ProjectInfo {
  type: 'node' | 'python' | 'java' | 'csharp' | 'go' | 'rust' | 'unknown';
  packageManager?: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'maven' | 'gradle' | 'cargo';
  framework?: string;
  dependencies: string[];
  testFramework?: string;
  buildTool?: string;
}

export interface AgentMemory {
  shortTerm: {
    currentTask?: string;
    recentActions: AgentAction[];
    contextFiles: string[];
    errors: string[];
  };
  longTerm: {
    userPreferences: { [key: string]: any };
    projectPatterns: { [key: string]: any };
    successfulStrategies: string[];
  };
}

export interface AgentAction {
  id: string;
  timestamp: number;
  tool: string;
  parameters: any;
  result?: any;
  error?: string;
  duration?: number;
}

export interface AgentThought {
  id: string;
  timestamp: number;
  content: string;
  type: 'analysis' | 'planning' | 'decision' | 'reflection';
  confidence: number;
}

export interface AgentDecision {
  action: string;
  reasoning: string;
  confidence: number;
  alternatives: string[];
  expectedOutcome: string;
}

export interface AgentCapability {
  name: string;
  description: string;
  tools: string[];
  complexity: 'basic' | 'intermediate' | 'advanced';
  domains: string[];
}

export interface AgentPersonality {
  name: string;
  description: string;
  systemPrompt: string;
  traits: {
    creativity: number;
    precision: number;
    autonomy: number;
    verbosity: number;
    riskTolerance: number;
  };
  specializations: string[];
}

export const DEFAULT_PERSONALITIES: { [key: string]: AgentPersonality } = {
  'balanced': {
    name: 'Balanced Assistant',
    description: 'Well-rounded assistant suitable for most tasks',
    systemPrompt: 'You are a helpful, balanced AI coding assistant.',
    traits: {
      creativity: 0.7,
      precision: 0.8,
      autonomy: 0.6,
      verbosity: 0.6,
      riskTolerance: 0.5
    },
    specializations: ['general', 'debugging', 'refactoring']
  },
  'precise': {
    name: 'Precision Expert',
    description: 'Highly accurate and methodical approach',
    systemPrompt: 'You are a precise, methodical AI coding assistant focused on accuracy.',
    traits: {
      creativity: 0.4,
      precision: 0.95,
      autonomy: 0.4,
      verbosity: 0.8,
      riskTolerance: 0.2
    },
    specializations: ['testing', 'debugging', 'code-review']
  },
  'creative': {
    name: 'Creative Innovator',
    description: 'Innovative solutions and creative problem-solving',
    systemPrompt: 'You are a creative, innovative AI coding assistant.',
    traits: {
      creativity: 0.9,
      precision: 0.6,
      autonomy: 0.8,
      verbosity: 0.5,
      riskTolerance: 0.8
    },
    specializations: ['architecture', 'optimization', 'new-features']
  }
};
