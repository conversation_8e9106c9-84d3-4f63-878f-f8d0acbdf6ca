import React, { useState, useEffect } from 'react';
import { Settings, Plus, Edit3, Send, AtSign } from 'lucide-react';
import { useVscodeApi } from './hooks/useVscodeApi';
import { ChatMode } from './modes/ChatMode';
import { AgentMode } from './modes/AgentMode';
import { AutoAgentMode } from './modes/AutoAgentMode';
import { SettingsPanel } from './components/SettingsPanel';
import { ModelSelector } from './components/ModelSelector';
import { ExtensionToWebviewMessage, WebviewToExtensionMessage } from '../../src/types/messaging';

type Mode = 'chat' | 'agent' | 'auto-agent';
type Tab = 'memories' | 'extension';

const App: React.FC = () => {
    const vscode = useVscodeApi();
    const [currentMode, setCurrentMode] = useState<Mode>('chat');
    const [currentTab, setCurrentTab] = useState<Tab>('memories');
    const [showSettings, setShowSettings] = useState(false);
    const [currentModel, setCurrentModel] = useState('gemini-2.0-flash-exp');
    const [availableModels, setAvailableModels] = useState<string[]>([]);
    const [messages, setMessages] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        // Request available models on startup
        vscode.postMessage({ type: 'getModels' });
        
        // Listen for messages from extension
        const handleMessage = (event: MessageEvent<ExtensionToWebviewMessage>) => {
            const message = event.data;
            
            switch (message.type) {
                case 'models':
                    setAvailableModels(message.payload.available);
                    setCurrentModel(message.payload.current);
                    break;
                case 'aiResponse':
                    setMessages(prev => [...prev, {
                        id: Date.now().toString(),
                        role: 'assistant',
                        content: message.payload,
                        timestamp: Date.now(),
                        mode: message.mode
                    }]);
                    setIsLoading(false);
                    break;
                case 'chatCleared':
                    setMessages([]);
                    break;
                case 'settingsShown':
                    setShowSettings(true);
                    break;
                case 'agentError':
                    setMessages(prev => [...prev, {
                        id: Date.now().toString(),
                        role: 'assistant',
                        content: `Error: ${message.payload}`,
                        timestamp: Date.now(),
                        mode: message.mode,
                        error: true
                    }]);
                    setIsLoading(false);
                    break;
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, [vscode]);

    const handleSendMessage = (content: string) => {
        // Add user message to chat
        const userMessage = {
            id: Date.now().toString(),
            role: 'user',
            content,
            timestamp: Date.now(),
            mode: currentMode,
            model: currentModel
        };
        
        setMessages(prev => [...prev, userMessage]);
        setIsLoading(true);

        // Send to extension
        const message: WebviewToExtensionMessage = {
            type: 'userQuery',
            payload: content,
            mode: currentMode,
            model: currentModel
        };
        
        vscode.postMessage(message);
    };

    const handleNewChat = () => {
        vscode.postMessage({ type: 'newChat' });
    };

    const handleModelChange = (model: string) => {
        setCurrentModel(model);
        vscode.postMessage({ type: 'setModel', payload: { model } });
    };

    const renderModeContent = () => {
        const commonProps = {
            messages,
            isLoading,
            onSendMessage: handleSendMessage
        };

        switch (currentMode) {
            case 'chat':
                return <ChatMode {...commonProps} />;
            case 'agent':
                return <AgentMode {...commonProps} />;
            case 'auto-agent':
                return <AutoAgentMode {...commonProps} />;
            default:
                return <ChatMode {...commonProps} />;
        }
    };

    if (showSettings) {
        return (
            <SettingsPanel 
                onClose={() => setShowSettings(false)}
                vscode={vscode}
            />
        );
    }

    return (
        <div className="app">
            {/* Header */}
            <div className="header">
                <div className="header-left">
                    <span className="header-title">SIA: CHAT</span>
                </div>
                <div className="header-right">
                    <div className="new-chat-section">
                        <select
                            className="new-chat-dropdown"
                            value="New Chat"
                            onChange={() => {}}
                        >
                            <option>New Chat</option>
                        </select>
                        <button
                            className="new-chat-btn"
                            onClick={handleNewChat}
                            title="New Chat"
                        >
                            <Plus size={16} />
                        </button>
                    </div>
                    <button
                        className="settings-btn"
                        onClick={() => setShowSettings(true)}
                        title="Settings"
                    >
                        <Settings size={18} />
                    </button>
                </div>
            </div>

            {/* Model Selector */}
            <ModelSelector
                currentModel={currentModel}
                availableModels={availableModels}
                onModelChange={handleModelChange}
            />

            {/* Main Content */}
            <div className="main-content">
                {renderModeContent()}
            </div>

            {/* Bottom Section */}
            <div className="bottom-section">
                <div className="tabs">
                    <button
                        className={`tab ${currentTab === 'memories' ? 'active' : ''}`}
                        onClick={() => setCurrentTab('memories')}
                    >
                        📚 SIA Memories
                    </button>
                    <button
                        className={`tab ${currentTab === 'extension' ? 'active' : ''}`}
                        onClick={() => setCurrentTab('extension')}
                    >
                        🧩 extension
                        <span className="beta-badge">Beta</span>
                    </button>
                </div>

                {/* Input Area */}
                <div className="input-area">
                    <div className="input-row">
                        <div className="input-container">
                            <textarea
                                className="message-input"
                                placeholder="Ask or instruct SIA Agent"
                                rows={1}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' && !e.shiftKey) {
                                        e.preventDefault();
                                        const content = e.currentTarget.value.trim();
                                        if (content && !isLoading) {
                                            handleSendMessage(content);
                                            e.currentTarget.value = '';
                                        }
                                    }
                                }}
                            />
                        </div>
                        <div className="agent-controls">
                            <button className="attach-btn" title="Attach context (@file, @url)">
                                <AtSign size={16} />
                            </button>
                            <div className="agent-mode-selector">
                                <Edit3 className="agent-mode-icon" size={16} />
                                <span>Agent</span>
                                <select
                                    className="agent-mode-dropdown"
                                    value={currentMode}
                                    onChange={(e) => setCurrentMode(e.target.value as Mode)}
                                >
                                    <option value="chat">Chat</option>
                                    <option value="agent">Agent</option>
                                    <option value="auto-agent">Auto</option>
                                </select>
                            </div>
                            <button
                                className="send-btn"
                                disabled={isLoading}
                                onClick={() => {
                                    const input = document.querySelector('.message-input') as HTMLTextAreaElement;
                                    const content = input?.value.trim();
                                    if (content && !isLoading) {
                                        handleSendMessage(content);
                                        input.value = '';
                                    }
                                }}
                                title="Send message"
                            >
                                {isLoading ? (
                                    <div className="loading-spinner" />
                                ) : (
                                    <Send size={18} />
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default App;
