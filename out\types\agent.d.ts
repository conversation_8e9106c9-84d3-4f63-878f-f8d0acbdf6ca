export interface AgentContext {
    currentMode: 'chat' | 'agent' | 'auto-agent';
    currentModel: string;
    conversationHistory: any[];
    workspaceRoot?: string;
    activeFiles: string[];
    recentErrors: string[];
    projectInfo?: ProjectInfo;
}
export interface ProjectInfo {
    type: 'node' | 'python' | 'java' | 'csharp' | 'go' | 'rust' | 'unknown';
    packageManager?: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'maven' | 'gradle' | 'cargo';
    framework?: string;
    dependencies: string[];
    testFramework?: string;
    buildTool?: string;
}
export interface AgentMemory {
    shortTerm: {
        currentTask?: string;
        recentActions: AgentAction[];
        contextFiles: string[];
        errors: string[];
    };
    longTerm: {
        userPreferences: {
            [key: string]: any;
        };
        projectPatterns: {
            [key: string]: any;
        };
        successfulStrategies: string[];
    };
}
export interface AgentAction {
    id: string;
    timestamp: number;
    tool: string;
    parameters: any;
    result?: any;
    error?: string;
    duration?: number;
}
export interface AgentThought {
    id: string;
    timestamp: number;
    content: string;
    type: 'analysis' | 'planning' | 'decision' | 'reflection';
    confidence: number;
}
export interface AgentDecision {
    action: string;
    reasoning: string;
    confidence: number;
    alternatives: string[];
    expectedOutcome: string;
}
export interface AgentCapability {
    name: string;
    description: string;
    tools: string[];
    complexity: 'basic' | 'intermediate' | 'advanced';
    domains: string[];
}
export interface AgentPersonality {
    name: string;
    description: string;
    systemPrompt: string;
    traits: {
        creativity: number;
        precision: number;
        autonomy: number;
        verbosity: number;
        riskTolerance: number;
    };
    specializations: string[];
}
export declare const DEFAULT_PERSONALITIES: {
    [key: string]: AgentPersonality;
};
//# sourceMappingURL=agent.d.ts.map