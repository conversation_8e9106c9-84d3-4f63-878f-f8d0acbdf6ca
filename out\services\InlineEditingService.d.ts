import * as vscode from 'vscode';
import { LLMService } from './LLMService';
import { ToolController } from '../controllers/ToolController';
export interface InlineEditRequest {
    type: 'edit' | 'refactor' | 'optimize' | 'addTests' | 'addDocs' | 'explain';
    code: string;
    filePath: string;
    selection: vscode.Selection;
    customPrompt?: string;
}
export interface InlineEditResult {
    success: boolean;
    originalCode: string;
    modifiedCode?: string;
    explanation?: string;
    error?: string;
    diffPreview?: string;
}
export declare class InlineEditingService {
    private static instance;
    private llmService;
    private toolController;
    private errorHandler;
    private constructor();
    static getInstance(llmService?: LLMService, toolController?: ToolController): InlineEditingService;
    /**
     * Show inline dialog for user input
     */
    showInlineDialog(type: string, selectedText: string): Promise<string | undefined>;
    /**
     * Process inline edit request
     */
    processInlineEdit(request: InlineEditRequest): Promise<InlineEditResult>;
    /**
     * Build prompt for LLM based on request type
     */
    private buildPrompt;
    /**
     * Extract code from LLM response
     */
    private extractCodeFromResponse;
    /**
     * Generate diff preview
     */
    private generateDiffPreview;
    /**
     * Apply changes to the active editor
     */
    applyChanges(editor: vscode.TextEditor, selection: vscode.Selection, newCode: string): Promise<boolean>;
    /**
     * Show diff preview in a new editor
     */
    showDiffPreview(originalCode: string, modifiedCode: string, filePath: string): Promise<void>;
    /**
     * Show explanation in a new editor
     */
    showExplanation(explanation: string, filePath: string): Promise<void>;
}
//# sourceMappingURL=InlineEditingService.d.ts.map