import * as vscode from 'vscode';

export class ApiKeyManager {
    private context: vscode.ExtensionContext;
    private readonly secretStorage: vscode.SecretStorage;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.secretStorage = context.secrets;
    }

    /**
     * Get API key for a specific provider
     */
    async getApiKey(provider: string): Promise<string | undefined> {
        try {
            const key = await this.secretStorage.get(`sia.apikey.${provider}`);
            return key;
        } catch (error) {
            console.error(`Error retrieving API key for ${provider}:`, error);
            return undefined;
        }
    }

    /**
     * Set API key for a specific provider
     */
    async setApiKey(provider: string, key: string): Promise<void> {
        try {
            await this.secretStorage.store(`sia.apikey.${provider}`, key);
            console.log(`API key stored for provider: ${provider}`);
        } catch (error) {
            console.error(`Error storing API key for ${provider}:`, error);
            throw error;
        }
    }

    /**
     * Remove API key for a specific provider
     */
    async removeApiKey(provider: string): Promise<void> {
        try {
            await this.secretStorage.delete(`sia.apikey.${provider}`);
            console.log(`API key removed for provider: ${provider}`);
        } catch (error) {
            console.error(`Error removing API key for ${provider}:`, error);
            throw error;
        }
    }

    /**
     * Check if API key exists for a provider
     */
    async hasApiKey(provider: string): Promise<boolean> {
        const key = await this.getApiKey(provider);
        return !!key;
    }

    /**
     * Get all configured providers
     */
    async getConfiguredProviders(): Promise<string[]> {
        const providers = ['gemini', 'mistral', 'deepseek', 'openai', 'anthropic'];
        const configured: string[] = [];

        for (const provider of providers) {
            if (await this.hasApiKey(provider)) {
                configured.push(provider);
            }
        }

        return configured;
    }

    /**
     * Prompt user for API key
     */
    async promptForApiKey(provider: string): Promise<string | undefined> {
        const key = await vscode.window.showInputBox({
            prompt: `Enter your ${provider.toUpperCase()} API key`,
            password: true,
            placeHolder: 'API key...',
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'API key cannot be empty';
                }
                return null;
            }
        });

        if (key) {
            await this.setApiKey(provider, key.trim());
            return key.trim();
        }

        return undefined;
    }

    /**
     * Get API key status for all providers
     */
    async getApiKeyStatus(): Promise<{ [provider: string]: boolean }> {
        const providers = ['gemini', 'mistral', 'deepseek', 'openai', 'anthropic'];
        const status: { [provider: string]: boolean } = {};

        for (const provider of providers) {
            status[provider] = await this.hasApiKey(provider);
        }

        return status;
    }

    /**
     * Validate API key format (basic validation)
     */
    validateApiKeyFormat(provider: string, key: string): boolean {
        if (!key || key.trim().length === 0) {
            return false;
        }

        switch (provider.toLowerCase()) {
            case 'gemini':
                return key.startsWith('AIza') && key.length > 30;
            case 'openai':
                return key.startsWith('sk-') && key.length > 40;
            case 'anthropic':
                return key.startsWith('sk-ant-') && key.length > 50;
            case 'mistral':
                return key.length > 20; // Basic length check
            case 'deepseek':
                return key.length > 20; // Basic length check
            default:
                return key.length > 10; // Minimum length for unknown providers
        }
    }
}
