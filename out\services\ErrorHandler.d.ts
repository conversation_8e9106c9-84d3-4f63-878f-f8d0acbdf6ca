export interface ErrorInfo {
    code: string;
    message: string;
    details?: any;
    timestamp: number;
    context?: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
}
export declare class ErrorHandler {
    private static instance;
    private errorLog;
    private maxLogSize;
    private constructor();
    static getInstance(): <PERSON>rrorHandler;
    /**
     * Handle and log an error
     */
    handleError(error: Error | string, context?: string, severity?: ErrorInfo['severity']): ErrorInfo;
    /**
     * Handle API errors specifically
     */
    handleApiError(error: any, provider: string): ErrorInfo;
    /**
     * Handle tool execution errors
     */
    handleToolError(toolName: string, error: any, parameters?: any): ErrorInfo;
    /**
     * Handle validation errors
     */
    handleValidationError(field: string, value: any, expectedFormat: string): ErrorInfo;
    /**
     * Handle user input errors
     */
    handleUserInputError(input: string, reason: string): ErrorInfo;
    /**
     * Log error to internal log
     */
    private logError;
    /**
     * Notify user based on error severity
     */
    private notifyUser;
    /**
     * Generate a unique error code
     */
    private generateErrorCode;
    /**
     * Show detailed error information
     */
    private showErrorDetails;
    /**
     * Log to VS Code output channel
     */
    private logToOutputChannel;
    /**
     * Get recent errors
     */
    getRecentErrors(count?: number): ErrorInfo[];
    /**
     * Get errors by severity
     */
    getErrorsBySeverity(severity: ErrorInfo['severity']): ErrorInfo[];
    /**
     * Clear error log
     */
    clearErrorLog(): void;
    /**
     * Get error statistics
     */
    getErrorStats(): {
        [severity: string]: number;
    };
    /**
     * Create a user-friendly error message
     */
    createUserFriendlyMessage(error: ErrorInfo): string;
    /**
     * Export error log for debugging
     */
    exportErrorLog(): string;
}
//# sourceMappingURL=ErrorHandler.d.ts.map