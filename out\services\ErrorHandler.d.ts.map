{"version": 3, "file": "ErrorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/services/ErrorHandler.ts"], "names": [], "mappings": "AAEA,MAAM,WAAW,SAAS;IACtB,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,GAAG,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;CACpD;AAED,qBAAa,YAAY;IACrB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAe;IACtC,OAAO,CAAC,QAAQ,CAAmB;IACnC,OAAO,CAAC,UAAU,CAAO;IAEzB,OAAO;IAEP,MAAM,CAAC,WAAW,IAAI,YAAY;IAOlC;;OAEG;IACH,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,EAAE,QAAQ,GAAE,SAAS,CAAC,UAAU,CAAY,GAAG,SAAS;IAgB3G;;OAEG;IACH,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,GAAG,SAAS;IA8BvD;;OAEG;IACH,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,CAAC,EAAE,GAAG,GAAG,SAAS;IAkB1E;;OAEG;IACH,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,cAAc,EAAE,MAAM,GAAG,SAAS;IAKnF;;OAEG;IACH,oBAAoB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,SAAS;IAK9D;;OAEG;IACH,OAAO,CAAC,QAAQ;IAgBhB;;OAEG;IACH,OAAO,CAAC,UAAU;IAwBlB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAMzB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAsBxB;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAU1B;;OAEG;IACH,eAAe,CAAC,KAAK,GAAE,MAAW,GAAG,SAAS,EAAE;IAIhD;;OAEG;IACH,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,EAAE;IAIjE;;OAEG;IACH,aAAa,IAAI,IAAI;IAIrB;;OAEG;IACH,aAAa,IAAI;QAAE,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE;IAU/C;;OAEG;IACH,yBAAyB,CAAC,KAAK,EAAE,SAAS,GAAG,MAAM;IAkBnD;;OAEG;IACH,cAAc,IAAI,MAAM;CAG3B"}