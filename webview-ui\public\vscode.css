/* VS Code theme variables */
:root {
  --vscode-font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  --vscode-font-weight: 400;
  --vscode-font-size: 13px;
  --vscode-editor-background: #1e1e1e;
  --vscode-editor-foreground: #d4d4d4;
  --vscode-sideBar-background: #252526;
  --vscode-sideBar-foreground: #cccccc;
  --vscode-activityBar-background: #333333;
  --vscode-statusBar-background: #007acc;
  --vscode-button-background: #0e639c;
  --vscode-button-foreground: #ffffff;
  --vscode-button-hoverBackground: #1177bb;
  --vscode-input-background: #3c3c3c;
  --vscode-input-foreground: #cccccc;
  --vscode-input-border: #3c3c3c;
  --vscode-dropdown-background: #3c3c3c;
  --vscode-dropdown-foreground: #cccccc;
  --vscode-dropdown-border: #3c3c3c;
  --vscode-list-hoverBackground: #2a2d2e;
  --vscode-list-activeSelectionBackground: #094771;
  --vscode-scrollbar-shadow: #000000;
  --vscode-widget-shadow: #0000005c;
  --vscode-panel-background: #1e1e1e;
  --vscode-panel-border: #3c3c3c;
  --vscode-textLink-foreground: #3794ff;
  --vscode-textCodeBlock-background: #0a0a0a;
  --vscode-textPreformat-foreground: #d7ba7d;
  --vscode-badge-background: #4d4d4d;
  --vscode-badge-foreground: #ffffff;
  --vscode-progressBar-background: #0e70c0;
  --vscode-editorWidget-background: #252526;
  --vscode-editorWidget-border: #454545;
  --vscode-editorHoverWidget-background: #252526;
  --vscode-editorHoverWidget-border: #454545;
}

body {
  font-family: var(--vscode-font-family);
  font-weight: var(--vscode-font-weight);
  font-size: var(--vscode-font-size);
  background-color: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

* {
  box-sizing: border-box;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-corner {
  background-color: var(--vscode-editor-background);
}

::-webkit-scrollbar-thumb {
  background-color: #424242;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #4f4f4f;
}

::-webkit-scrollbar-track {
  background-color: var(--vscode-editor-background);
}

/* Button styling */
button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  padding: 6px 14px;
  border-radius: 2px;
  cursor: pointer;
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  transition: background-color 0.2s;
}

button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Input styling */
input, textarea, select {
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border: 1px solid var(--vscode-input-border);
  padding: 6px 8px;
  border-radius: 2px;
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
}

input:focus, textarea:focus, select:focus {
  outline: 1px solid var(--vscode-statusBar-background);
  outline-offset: -1px;
}

/* Code styling */
code {
  background-color: var(--vscode-textCodeBlock-background);
  color: var(--vscode-textPreformat-foreground);
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 12px;
}

pre {
  background-color: var(--vscode-textCodeBlock-background);
  color: var(--vscode-editor-foreground);
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

/* Link styling */
a {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
