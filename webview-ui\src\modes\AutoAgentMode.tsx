import React from 'react';
import { MessageList } from '../components/MessageList';

interface AutoAgentModeProps {
    messages: any[];
    isLoading: boolean;
    onSendMessage: (content: string) => void;
}

export const AutoAgentMode: React.FC<AutoAgentModeProps> = ({ messages, isLoading, onSendMessage }) => {
    return (
        <div className="auto-agent-mode">
            <div className="chat-area">
                {messages.length === 0 && !isLoading ? (
                    <div className="empty-state">
                        <div>
                            <h3>Auto-Agent Mode - Autonomous Execution</h3>
                            <p>Give me a high-level goal and I'll work autonomously to achieve it.</p>
                            <div style={{ marginTop: '16px', fontSize: '12px', opacity: 0.7 }}>
                                <p>• I work independently with minimal supervision</p>
                                <p>• I'll stream my thoughts and actions in real-time</p>
                                <p>• I can self-correct and adapt to challenges</p>
                                <p>• Use the stop button if I go off track</p>
                            </div>
                        </div>
                    </div>
                ) : (
                    <MessageList messages={messages} isLoading={isLoading} />
                )}
            </div>
        </div>
    );
};
