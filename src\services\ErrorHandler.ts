import * as vscode from 'vscode';

export interface ErrorInfo {
    code: string;
    message: string;
    details?: any;
    timestamp: number;
    context?: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
}

export class ErrorHandler {
    private static instance: ErrorHandler;
    private errorLog: ErrorInfo[] = [];
    private maxLogSize = 100;

    private constructor() {}

    static getInstance(): ErrorHandler {
        if (!ErrorHandler.instance) {
            ErrorHandler.instance = new ErrorHandler();
        }
        return ErrorHandler.instance;
    }

    /**
     * Handle and log an error
     */
    handleError(error: Error | string, context?: string, severity: ErrorInfo['severity'] = 'medium'): ErrorInfo {
        const errorInfo: ErrorInfo = {
            code: this.generateErrorCode(error),
            message: error instanceof Error ? error.message : error,
            details: error instanceof Error ? error.stack : undefined,
            timestamp: Date.now(),
            context,
            severity
        };

        this.logError(errorInfo);
        this.notifyUser(errorInfo);
        
        return errorInfo;
    }

    /**
     * Handle API errors specifically
     */
    handleApiError(error: any, provider: string): ErrorInfo {
        let message = 'API request failed';
        let severity: ErrorInfo['severity'] = 'medium';

        if (error.response) {
            // HTTP error response
            const status = error.response.status;
            if (status === 401) {
                message = `Invalid API key for ${provider}`;
                severity = 'high';
            } else if (status === 429) {
                message = `Rate limit exceeded for ${provider}`;
                severity = 'medium';
            } else if (status >= 500) {
                message = `${provider} service unavailable`;
                severity = 'high';
            } else {
                message = `${provider} API error: ${error.response.statusText}`;
            }
        } else if (error.code === 'ENOTFOUND') {
            message = `Network error: Cannot reach ${provider} API`;
            severity = 'high';
        } else if (error.code === 'ETIMEDOUT') {
            message = `Request timeout for ${provider} API`;
            severity = 'medium';
        }

        return this.handleError(new Error(message), `API:${provider}`, severity);
    }

    /**
     * Handle tool execution errors
     */
    handleToolError(toolName: string, error: any, parameters?: any): ErrorInfo {
        const message = `Tool '${toolName}' failed: ${error.message || error}`;
        const context = `Tool:${toolName}`;
        
        let severity: ErrorInfo['severity'] = 'medium';
        
        // Determine severity based on tool type
        if (['writeFile', 'deleteFile', 'moveFile'].includes(toolName)) {
            severity = 'high'; // File modification errors are serious
        } else if (['runInTerminal', 'runTests'].includes(toolName)) {
            severity = 'medium'; // Execution errors are moderately serious
        } else {
            severity = 'low'; // Read-only operations are less critical
        }

        return this.handleError(error, context, severity);
    }

    /**
     * Handle validation errors
     */
    handleValidationError(field: string, value: any, expectedFormat: string): ErrorInfo {
        const message = `Invalid ${field}: expected ${expectedFormat}, got ${typeof value}`;
        return this.handleError(new Error(message), 'Validation', 'low');
    }

    /**
     * Handle user input errors
     */
    handleUserInputError(input: string, reason: string): ErrorInfo {
        const message = `Invalid user input: ${reason}`;
        return this.handleError(new Error(message), 'UserInput', 'low');
    }

    /**
     * Log error to internal log
     */
    private logError(errorInfo: ErrorInfo): void {
        this.errorLog.unshift(errorInfo);
        
        // Keep log size manageable
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(0, this.maxLogSize);
        }

        // Log to console for debugging
        console.error(`[SIA Error] ${errorInfo.code}: ${errorInfo.message}`, {
            context: errorInfo.context,
            severity: errorInfo.severity,
            details: errorInfo.details
        });
    }

    /**
     * Notify user based on error severity
     */
    private notifyUser(errorInfo: ErrorInfo): void {
        const message = `SIA: ${errorInfo.message}`;
        
        switch (errorInfo.severity) {
            case 'critical':
                vscode.window.showErrorMessage(message, 'View Details').then(selection => {
                    if (selection === 'View Details') {
                        this.showErrorDetails(errorInfo);
                    }
                });
                break;
            case 'high':
                vscode.window.showErrorMessage(message);
                break;
            case 'medium':
                vscode.window.showWarningMessage(message);
                break;
            case 'low':
                // Only show in output channel for low severity
                this.logToOutputChannel(errorInfo);
                break;
        }
    }

    /**
     * Generate a unique error code
     */
    private generateErrorCode(error: Error | string): string {
        const timestamp = Date.now().toString(36);
        const errorType = error instanceof Error ? error.constructor.name : 'StringError';
        return `SIA_${errorType}_${timestamp}`;
    }

    /**
     * Show detailed error information
     */
    private showErrorDetails(errorInfo: ErrorInfo): void {
        const details = [
            `Error Code: ${errorInfo.code}`,
            `Message: ${errorInfo.message}`,
            `Severity: ${errorInfo.severity}`,
            `Context: ${errorInfo.context || 'Unknown'}`,
            `Timestamp: ${new Date(errorInfo.timestamp).toISOString()}`,
            ''
        ];

        if (errorInfo.details) {
            details.push('Stack Trace:', errorInfo.details);
        }

        vscode.workspace.openTextDocument({
            content: details.join('\n'),
            language: 'plaintext'
        }).then(doc => {
            vscode.window.showTextDocument(doc);
        });
    }

    /**
     * Log to VS Code output channel
     */
    private logToOutputChannel(errorInfo: ErrorInfo): void {
        const outputChannel = vscode.window.createOutputChannel('SIA Assistant');
        const timestamp = new Date(errorInfo.timestamp).toISOString();
        outputChannel.appendLine(`[${timestamp}] ${errorInfo.severity.toUpperCase()}: ${errorInfo.message}`);
        
        if (errorInfo.context) {
            outputChannel.appendLine(`  Context: ${errorInfo.context}`);
        }
    }

    /**
     * Get recent errors
     */
    getRecentErrors(count: number = 10): ErrorInfo[] {
        return this.errorLog.slice(0, count);
    }

    /**
     * Get errors by severity
     */
    getErrorsBySeverity(severity: ErrorInfo['severity']): ErrorInfo[] {
        return this.errorLog.filter(error => error.severity === severity);
    }

    /**
     * Clear error log
     */
    clearErrorLog(): void {
        this.errorLog = [];
    }

    /**
     * Get error statistics
     */
    getErrorStats(): { [severity: string]: number } {
        const stats = { low: 0, medium: 0, high: 0, critical: 0 };
        
        for (const error of this.errorLog) {
            stats[error.severity]++;
        }
        
        return stats;
    }

    /**
     * Create a user-friendly error message
     */
    createUserFriendlyMessage(error: ErrorInfo): string {
        const suggestions: { [key: string]: string } = {
            'API': 'Please check your API key configuration in settings.',
            'Tool': 'This operation failed. Please try again or check file permissions.',
            'Validation': 'Please check your input format and try again.',
            'UserInput': 'Please provide valid input and try again.',
            'Network': 'Please check your internet connection and try again.'
        };

        let suggestion = '';
        if (error.context) {
            const contextType = error.context.split(':')[0];
            suggestion = suggestions[contextType] || 'Please try again or contact support.';
        }

        return `${error.message}\n\n${suggestion}`;
    }

    /**
     * Export error log for debugging
     */
    exportErrorLog(): string {
        return JSON.stringify(this.errorLog, null, 2);
    }
}
