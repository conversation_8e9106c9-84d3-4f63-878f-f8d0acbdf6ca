/**
 * Advanced System Prompts for SIA AI Assistant
 * Inspired by top AI coding assistants and best practices
 */
export declare class SystemPrompts {
    /**
     * Get enhanced system prompt for chat mode
     */
    static getChatModePrompt(): string;
    /**
     * Get enhanced system prompt for agent mode
     */
    static getAgentModePrompt(): string;
    /**
     * Get enhanced system prompt for auto-agent mode
     */
    static getAutoAgentModePrompt(): string;
    /**
     * Get context-aware system prompt based on project type and current state
     */
    static getContextualPrompt(projectInfo: any, mode: string, context: any): string;
    /**
     * Get specialized prompts for specific tasks
     */
    static getTaskSpecificPrompt(task: string): string;
}
//# sourceMappingURL=SystemPrompts.d.ts.map