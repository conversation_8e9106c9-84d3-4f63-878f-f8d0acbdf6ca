import * as vscode from 'vscode';
import { LLMService } from './LLMService';
import { ToolController } from '../controllers/ToolController';
import { <PERSON>rrorHandler } from './ErrorHandler';

export interface InlineEditRequest {
    type: 'edit' | 'refactor' | 'optimize' | 'addTests' | 'addDocs' | 'explain';
    code: string;
    filePath: string;
    selection: vscode.Selection;
    customPrompt?: string;
}

export interface InlineEditResult {
    success: boolean;
    originalCode: string;
    modifiedCode?: string;
    explanation?: string;
    error?: string;
    diffPreview?: string;
}

export class InlineEditingService {
    private static instance: InlineEditingService;
    private llmService: LLMService;
    private toolController: ToolController;
    private errorHandler: ErrorHandler;

    private constructor(llmService: LLMService, toolController: ToolController) {
        this.llmService = llmService;
        this.toolController = toolController;
        this.errorHandler = ErrorHandler.getInstance();
    }

    static getInstance(llmService?: LLMService, toolController?: ToolController): InlineEditingService {
        if (!InlineEditingService.instance) {
            if (!llmService || !toolController) {
                throw new Error('LLMService and ToolController are required for first initialization');
            }
            InlineEditingService.instance = new InlineEditingService(llmService, toolController);
        }
        return InlineEditingService.instance;
    }

    /**
     * Show inline dialog for user input
     */
    async showInlineDialog(type: string, selectedText: string): Promise<string | undefined> {
        const prompts = {
            edit: 'Describe how you want to modify this code:',
            refactor: 'How would you like to refactor this code?',
            optimize: 'What optimization would you like to apply?',
            addTests: 'What type of tests should be added?',
            addDocs: 'What documentation should be added?',
            explain: 'What would you like me to explain about this code?'
        };

        const placeholder = prompts[type as keyof typeof prompts] || 'Enter your request:';
        
        const userInput = await vscode.window.showInputBox({
            prompt: placeholder,
            placeHolder: 'Type your request here...',
            value: '',
            ignoreFocusOut: true
        });

        return userInput;
    }

    /**
     * Process inline edit request
     */
    async processInlineEdit(request: InlineEditRequest): Promise<InlineEditResult> {
        try {
            const prompt = this.buildPrompt(request);
            
            // Use LLM to generate the modification
            const result = await this.llmService.generateContent(prompt, [], '');

            const responseText = result.response?.text() || '';
            if (!responseText) {
                return {
                    success: false,
                    originalCode: request.code,
                    error: 'Failed to generate code modification'
                };
            }

            const modifiedCode = this.extractCodeFromResponse(responseText, request.type);

            if (request.type === 'explain') {
                return {
                    success: true,
                    originalCode: request.code,
                    explanation: responseText
                };
            }

            const diffPreview = this.generateDiffPreview(request.code, modifiedCode);

            return {
                success: true,
                originalCode: request.code,
                modifiedCode: modifiedCode,
                diffPreview: diffPreview
            };

        } catch (error: any) {
            const errorInfo = this.errorHandler.handleError(error, 'InlineEditing', 'medium');
            return {
                success: false,
                originalCode: request.code,
                error: errorInfo.message
            };
        }
    }

    /**
     * Build prompt for LLM based on request type
     */
    private buildPrompt(request: InlineEditRequest): string {
        const baseContext = `File: ${request.filePath}\n\nCode to modify:\n\`\`\`\n${request.code}\n\`\`\`\n\n`;
        
        const prompts = {
            edit: `${baseContext}User request: ${request.customPrompt}\n\nPlease modify the code according to the user's request. Return only the modified code without explanations.`,
            
            refactor: `${baseContext}Please refactor this code to improve its structure, readability, and maintainability. ${request.customPrompt ? `Specific request: ${request.customPrompt}` : ''}\n\nReturn only the refactored code without explanations.`,
            
            optimize: `${baseContext}Please optimize this code for better performance and efficiency. ${request.customPrompt ? `Focus on: ${request.customPrompt}` : ''}\n\nReturn only the optimized code without explanations.`,
            
            addTests: `${baseContext}Please add comprehensive unit tests for this code. ${request.customPrompt ? `Test requirements: ${request.customPrompt}` : ''}\n\nReturn the test code that should be added.`,
            
            addDocs: `${baseContext}Please add comprehensive documentation to this code including JSDoc comments, inline comments, and explanations. ${request.customPrompt ? `Documentation focus: ${request.customPrompt}` : ''}\n\nReturn the code with added documentation.`,
            
            explain: `${baseContext}Please explain this code in detail. ${request.customPrompt ? `Focus on: ${request.customPrompt}` : 'Explain what it does, how it works, and any important details.'}\n\nProvide a clear, comprehensive explanation.`
        };

        return prompts[request.type] || prompts.edit;
    }

    /**
     * Extract code from LLM response
     */
    private extractCodeFromResponse(response: string, type: string): string {
        if (type === 'explain') {
            return response;
        }

        // Try to extract code from markdown code blocks
        const codeBlockRegex = /```(?:\w+)?\n?([\s\S]*?)\n?```/g;
        const matches = [...response.matchAll(codeBlockRegex)];
        
        if (matches.length > 0) {
            return matches[0][1].trim();
        }

        // If no code blocks found, return the response as-is (might be plain code)
        return response.trim();
    }

    /**
     * Generate diff preview
     */
    private generateDiffPreview(original: string, modified: string): string {
        const originalLines = original.split('\n');
        const modifiedLines = modified.split('\n');
        
        let diff = '';
        const maxLines = Math.max(originalLines.length, modifiedLines.length);
        
        for (let i = 0; i < maxLines; i++) {
            const originalLine = originalLines[i] || '';
            const modifiedLine = modifiedLines[i] || '';
            
            if (originalLine !== modifiedLine) {
                if (originalLine) {
                    diff += `- ${originalLine}\n`;
                }
                if (modifiedLine) {
                    diff += `+ ${modifiedLine}\n`;
                }
            } else if (originalLine) {
                diff += `  ${originalLine}\n`;
            }
        }
        
        return diff;
    }

    /**
     * Apply changes to the active editor
     */
    async applyChanges(
        editor: vscode.TextEditor, 
        selection: vscode.Selection, 
        newCode: string
    ): Promise<boolean> {
        try {
            const edit = new vscode.WorkspaceEdit();
            edit.replace(editor.document.uri, selection, newCode);
            
            const success = await vscode.workspace.applyEdit(edit);
            
            if (success) {
                // Format the document after applying changes
                await vscode.commands.executeCommand('editor.action.formatDocument');
            }
            
            return success;
        } catch (error: any) {
            this.errorHandler.handleError(error, 'InlineEditing:ApplyChanges', 'high');
            return false;
        }
    }

    /**
     * Show diff preview in a new editor
     */
    async showDiffPreview(originalCode: string, modifiedCode: string, filePath: string): Promise<void> {
        try {
            const diffContent = this.generateDiffPreview(originalCode, modifiedCode);
            
            const doc = await vscode.workspace.openTextDocument({
                content: diffContent,
                language: 'diff'
            });
            
            await vscode.window.showTextDocument(doc, {
                viewColumn: vscode.ViewColumn.Beside,
                preview: true
            });
            
        } catch (error: any) {
            this.errorHandler.handleError(error, 'InlineEditing:ShowDiff', 'medium');
        }
    }

    /**
     * Show explanation in a new editor
     */
    async showExplanation(explanation: string, filePath: string): Promise<void> {
        try {
            const doc = await vscode.workspace.openTextDocument({
                content: `# Code Explanation\n\nFile: ${filePath}\n\n${explanation}`,
                language: 'markdown'
            });
            
            await vscode.window.showTextDocument(doc, {
                viewColumn: vscode.ViewColumn.Beside,
                preview: true
            });
            
        } catch (error: any) {
            this.errorHandler.handleError(error, 'InlineEditing:ShowExplanation', 'medium');
        }
    }
}
