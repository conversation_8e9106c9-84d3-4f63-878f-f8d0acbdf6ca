import * as vscode from 'vscode';
export interface ContextItem {
    type: 'file' | 'directory' | 'url' | 'selection' | 'workspace';
    path: string;
    content?: string;
    metadata?: any;
}
export declare class ContextManager {
    private context;
    private contextItems;
    constructor(context: vscode.ExtensionContext);
    /**
     * Parse @ mentions from user input and resolve them to context items
     */
    parseContextMentions(input: string): Promise<{
        cleanInput: string;
        contextItems: ContextItem[];
    }>;
    /**
     * Resolve a context mention to a context item
     */
    private resolveContextMention;
    /**
     * Create context item for a file
     */
    private createFileContextItem;
    /**
     * Create context item for a directory
     */
    private createDirectoryContextItem;
    /**
     * Create context item for a URL
     */
    private createUrlContextItem;
    /**
     * Fuzzy match a path against workspace files
     */
    private fuzzyMatchPath;
    /**
     * Get directory structure recursively
     */
    private getDirectoryStructure;
    /**
     * Extract title from HTML content
     */
    private extractTitle;
    /**
     * Extract text content from HTML
     */
    private extractTextContent;
    /**
     * Get current editor selection as context
     */
    getCurrentSelection(): ContextItem | null;
    /**
     * Format context items for inclusion in prompts
     */
    formatContextForPrompt(contextItems: ContextItem[]): string;
    /**
     * Cache context item for reuse
     */
    cacheContextItem(key: string, item: ContextItem): void;
    /**
     * Get cached context item
     */
    getCachedContextItem(key: string): ContextItem | undefined;
    /**
     * Clear context cache
     */
    clearCache(): void;
}
//# sourceMappingURL=ContextManager.d.ts.map