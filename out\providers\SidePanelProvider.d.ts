import * as vscode from 'vscode';
import { WebviewToExtensionMessage, ExtensionToWebviewMessage } from '../types/messaging';
export declare class SidePanelProvider implements vscode.WebviewViewProvider {
    private readonly _extensionUri;
    static readonly viewType = "sia.chatView";
    private _view?;
    private _messageHandler?;
    constructor(_extensionUri: vscode.Uri);
    resolveWebviewView(webviewView: vscode.WebviewView, context: vscode.WebviewViewResolveContext, _token: vscode.CancellationToken): void;
    setMessageHandler(handler: (message: WebviewToExtensionMessage) => Promise<void>): void;
    sendMessage(message: ExtensionToWebviewMessage): void;
    newChat(): void;
    showSettings(): void;
    private _getHtmlForWebview;
}
//# sourceMappingURL=SidePanelProvider.d.ts.map