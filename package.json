{"name": "sia-ai-assistant", "displayName": "SIA - AI Coding Assistant", "description": "Advanced AI-powered coding assistant with multi-model support and intelligent tool orchestration", "version": "1.0.0", "publisher": "sia-dev", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets", "Education"], "keywords": ["ai", "assistant", "coding", "gemini", "mistral", "deepseek", "agent", "automation"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "sia-assistant", "title": "SIA Assistant", "icon": "$(robot)"}]}, "views": {"sia-assistant": [{"type": "webview", "id": "sia.chat<PERSON>iew", "name": "Cha<PERSON>", "when": "true"}]}, "commands": [{"command": "sia.start", "title": "Start SIA Assistant", "category": "SIA"}, {"command": "sia.newChat", "title": "New Chat", "category": "SIA"}, {"command": "sia.settings", "title": "Settings", "category": "SIA"}, {"command": "sia.refresh", "title": "Refresh", "icon": "$(refresh)"}, {"command": "sia.inlineEdit", "title": "SIA: Edit with AI", "icon": "$(edit)"}, {"command": "sia.inlineRefactor", "title": "SIA: Refactor Code", "icon": "$(symbol-method)"}, {"command": "sia.inlineOptimize", "title": "SIA: Optimize Code", "icon": "$(rocket)"}, {"command": "sia.inlineAddTests", "title": "SIA: Add Tests", "icon": "$(beaker)"}, {"command": "sia.inlineAddDocs", "title": "SIA: Add Documentation", "icon": "$(book)"}, {"command": "sia.inlineExplain", "title": "SIA: Explain Code", "icon": "$(question)"}], "keybindings": [{"command": "sia.start", "key": "ctrl+shift+s", "mac": "cmd+shift+s"}], "configuration": {"title": "SIA AI Assistant", "properties": {"sia.defaultModel": {"type": "string", "default": "gemini-2.0-flash", "description": "Default AI model to use", "enum": ["gemini-2.0-flash", "gemini-2.5-flash", "gemini-1.5-flash", "gpt-4o", "claude-3-5-sonnet-20241022"]}, "sia.defaultProvider": {"type": "string", "default": "gemini", "description": "Default AI provider", "enum": ["gemini", "openai", "anthropic", "mistral", "deepseek"]}, "sia.maxTokens": {"type": "number", "default": 4096, "minimum": 1, "maximum": 32768, "description": "Maximum tokens per request"}, "sia.temperature": {"type": "number", "default": 0.7, "minimum": 0, "maximum": 2, "description": "Model temperature (creativity level)"}, "sia.enableLogging": {"type": "boolean", "default": true, "description": "Enable detailed logging"}, "sia.autoSave": {"type": "boolean", "default": true, "description": "Automatically save changes"}, "sia.contextWindow": {"type": "number", "default": 8192, "minimum": 1024, "maximum": 131072, "description": "Context window size"}, "sia.maxFileSize": {"type": "number", "default": 1048576, "minimum": 1024, "maximum": 10485760, "description": "Maximum file size to process (bytes)"}, "sia.theme": {"type": "string", "default": "auto", "enum": ["dark", "light", "auto"], "description": "UI theme preference"}, "sia.panelPosition": {"type": "string", "default": "side", "enum": ["side", "bottom", "right"], "description": "Panel position in VS Code"}}}, "menus": {"editor/context": [{"command": "sia.inlineEdit", "when": "editorTextFocus", "group": "sia@1"}, {"command": "sia.inlineRefactor", "when": "editorHasSelection", "group": "sia@2"}, {"command": "sia.inlineOptimize", "when": "editorHasSelection", "group": "sia@3"}, {"command": "sia.inlineAddTests", "when": "editorHasSelection", "group": "sia@4"}, {"command": "sia.inlineAddDocs", "when": "editorHasSelection", "group": "sia@5"}, {"command": "sia.inlineExplain", "when": "editorHasSelection", "group": "sia@6"}], "commandPalette": [{"command": "sia.start"}, {"command": "sia.newChat"}, {"command": "sia.settings"}, {"command": "sia.inlineEdit"}, {"command": "sia.inlineRefactor"}, {"command": "sia.inlineOptimize"}, {"command": "sia.inlineAddTests"}, {"command": "sia.inlineAddDocs"}, {"command": "sia.inlineExplain"}]}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/node": "16.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "ts-loader": "^9.4.1", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "dependencies": {"@google/generative-ai": "^0.21.0", "axios": "^1.6.0", "glob": "^8.1.0", "node-fetch": "^3.3.2", "typescript": "^4.9.4"}}