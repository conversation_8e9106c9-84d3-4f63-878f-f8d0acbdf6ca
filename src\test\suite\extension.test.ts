import * as assert from 'assert';
import * as vscode from 'vscode';
import { ApiKeyManager } from '../../managers/ApiKeyManager';
import { LLMService } from '../../services/LLMService';
import { ToolController } from '../../controllers/ToolController';
import { ContextManager } from '../../services/ContextManager';

suite('SIA Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    let context: vscode.ExtensionContext;
    let apiKeyManager: ApiKeyManager;
    let llmService: LLMService;
    let toolController: ToolController;
    let contextManager: ContextManager;

    suiteSetup(async () => {
        // Get extension context
        const extension = vscode.extensions.getExtension('sia-dev.sia-ai-assistant');
        if (extension) {
            context = extension.exports?.context || {} as vscode.ExtensionContext;
        }

        // Initialize services
        apiKeyManager = new ApiKeyManager(context);
        llmService = new LLMService(apiKeyManager);
        toolController = new ToolController(context);
        contextManager = new ContextManager(context);
    });

    suite('ApiKeyManager Tests', () => {
        test('Should validate API key formats correctly', async () => {
            // Test Gemini key validation
            assert.strictEqual(
                apiKeyManager.validateApiKeyFormat('gemini', 'AIzaSyDc7u7wTVdDG3zP18xnELKs0HX7-hImkmc'),
                true
            );
            assert.strictEqual(
                apiKeyManager.validateApiKeyFormat('gemini', 'invalid-key'),
                false
            );

            // Test OpenAI key validation
            assert.strictEqual(
                apiKeyManager.validateApiKeyFormat('openai', 'sk-1234567890abcdef1234567890abcdef1234567890abcdef'),
                true
            );
            assert.strictEqual(
                apiKeyManager.validateApiKeyFormat('openai', 'invalid-key'),
                false
            );
        });

        test('Should store and retrieve API keys securely', async () => {
            const testKey = 'test-api-key-12345';
            await apiKeyManager.setApiKey('test-provider', testKey);
            
            const retrievedKey = await apiKeyManager.getApiKey('test-provider');
            assert.strictEqual(retrievedKey, testKey);

            const hasKey = await apiKeyManager.hasApiKey('test-provider');
            assert.strictEqual(hasKey, true);
        });
    });

    suite('LLMService Tests', () => {
        test('Should initialize with supported providers', () => {
            const providers = Object.keys(llmService.constructor.prototype.SUPPORTED_PROVIDERS || {});
            assert.ok(providers.length > 0, 'Should have supported providers');
        });

        test('Should set and get current model', async () => {
            try {
                await llmService.setModel('gemini-2.0-flash');
                const current = llmService.getCurrentModel();
                assert.strictEqual(current.model, 'gemini-2.0-flash');
            } catch (error) {
                // Expected if no API key is set
                assert.ok(error instanceof Error);
            }
        });
    });

    suite('ToolController Tests', () => {
        test('Should register all required tools', () => {
            const toolDefinitions = toolController.getToolDefinitions();
            const requiredTools = [
                'readFile', 'writeFile', 'createFile', 'readDirectory',
                'getProjectInfo', 'runInTerminal', 'webSearch',
                'generateTests', 'refactorCode', 'optimizeCode'
            ];

            for (const tool of requiredTools) {
                const found = toolDefinitions.find(t => t.name === tool);
                assert.ok(found, `Tool ${tool} should be registered`);
            }
        });

        test('Should execute file system tools correctly', async () => {
            // Test file existence check
            const result = await toolController.executeTool('fileExists', { path: 'package.json' });
            assert.strictEqual(result.success, true);
            assert.strictEqual(result.data, true);
        });

        test('Should handle invalid tool execution gracefully', async () => {
            const result = await toolController.executeTool('nonexistent-tool', {});
            assert.strictEqual(result.success, false);
            assert.ok(result.error?.includes('not found'));
        });
    });

    suite('ContextManager Tests', () => {
        test('Should parse @ mentions correctly', async () => {
            const input = 'Check @package.json and @src/extension.ts for issues';
            const { cleanInput, contextItems } = await contextManager.parseContextMentions(input);
            
            assert.ok(cleanInput.length < input.length, 'Should remove mentions from input');
            assert.ok(contextItems.length > 0, 'Should find context items');
        });

        test('Should handle URL mentions', async () => {
            const input = 'Check @https://example.com for documentation';
            const { cleanInput, contextItems } = await contextManager.parseContextMentions(input);
            
            assert.ok(contextItems.length > 0, 'Should find URL context item');
            assert.strictEqual(contextItems[0].type, 'url');
        });

        test('Should format context for prompts', () => {
            const contextItems = [
                {
                    type: 'file' as const,
                    path: 'test.ts',
                    content: 'console.log("test");',
                    metadata: { size: 20, extension: '.ts' }
                }
            ];

            const formatted = contextManager.formatContextForPrompt(contextItems);
            assert.ok(formatted.includes('CONTEXT PROVIDED'), 'Should include context header');
            assert.ok(formatted.includes('test.ts'), 'Should include file path');
            assert.ok(formatted.includes('console.log'), 'Should include file content');
        });
    });

    suite('Integration Tests', () => {
        test('Should handle complete workflow', async () => {
            // Test a complete workflow from user input to tool execution
            const query = 'Check @package.json and tell me about the project';
            
            // Parse context
            const { cleanInput, contextItems } = await contextManager.parseContextMentions(query);
            assert.ok(contextItems.length > 0, 'Should parse context items');
            
            // Execute tool
            const projectInfo = await toolController.executeTool('getProjectInfo', {});
            assert.strictEqual(projectInfo.success, true, 'Should get project info');
            
            // Format context
            const formatted = contextManager.formatContextForPrompt(contextItems);
            assert.ok(formatted.length > 0, 'Should format context');
        });

        test('Should handle errors gracefully', async () => {
            // Test error handling in tool execution
            const result = await toolController.executeTool('readFile', { path: 'nonexistent-file.txt' });
            assert.strictEqual(result.success, false);
            assert.ok(result.error, 'Should provide error message');
        });
    });

    suite('Performance Tests', () => {
        test('Should handle large context efficiently', async () => {
            const start = Date.now();
            
            // Test with multiple context items
            const input = '@package.json @src/extension.ts @README.md @tsconfig.json';
            await contextManager.parseContextMentions(input);
            
            const duration = Date.now() - start;
            assert.ok(duration < 5000, 'Should parse context within 5 seconds');
        });

        test('Should cache context items', () => {
            const testItem = {
                type: 'file' as const,
                path: 'test.ts',
                content: 'test content'
            };

            contextManager.cacheContextItem('test-key', testItem);
            const cached = contextManager.getCachedContextItem('test-key');
            
            assert.deepStrictEqual(cached, testItem, 'Should cache and retrieve items correctly');
        });
    });
});
