import { GenerateContentResult } from '@google/generative-ai';
import { ApiKeyManager } from '../managers/ApiKeyManager';
import { ToolDefinition } from '../types/tools';
export interface LLMProvider {
    name: string;
    models: string[];
    defaultModel: string;
    apiEndpoint?: string;
}
export declare const SUPPORTED_PROVIDERS: {
    [key: string]: LLMProvider;
};
export declare class LLMService {
    private apiKeyManager;
    private currentProvider;
    private currentModel;
    private geminiClient?;
    private errorHandler;
    constructor(apiKeyManager: ApiKeyManager);
    /**
     * Initialize the service with API keys
     */
    initialize(): Promise<void>;
    /**
     * Set current model and provider
     */
    setModel(model: string): Promise<void>;
    /**
     * Get current model info
     */
    getCurrentModel(): {
        provider: string;
        model: string;
    };
    /**
     * Get available models
     */
    getAvailableModels(): Promise<{
        provider: string;
        models: string[];
    }[]>;
    /**
     * Generate content using the current model
     */
    generateContent(prompt: string | any[], tools?: ToolDefinition[], systemPrompt?: string): Promise<GenerateContentResult>;
    /**
     * Generate content with Gemini
     */
    private generateWithGemini;
    /**
     * Generate content with other providers (OpenAI-compatible API)
     */
    private generateWithGenericProvider;
    /**
     * Stream content generation (for auto-agent mode)
     */
    streamContent(prompt: string | any[], tools?: ToolDefinition[], systemPrompt?: string): AsyncGenerator<GenerateContentResult>;
    /**
     * Test API key for a provider
     */
    testApiKey(provider: string): Promise<boolean>;
}
//# sourceMappingURL=LLMService.d.ts.map