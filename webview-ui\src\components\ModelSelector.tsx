import React from 'react';

interface ModelSelectorProps {
    currentModel: string;
    availableModels: string[];
    onModelChange: (model: string) => void;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
    currentModel,
    availableModels,
    onModelChange
}) => {
    const getModelDisplayName = (model: string) => {
        const modelMap: { [key: string]: string } = {
            'gemini-2.0-flash': 'Gemini 2.0 Flash',
            'gemini-2.5-flash': 'Gemini 2.5 flash',
            'gemini-1.5-flash': 'Gemini 1.5 Flash',
            'mistral-large-latest': 'Mistral Large',
            'mistral-medium-latest': 'Mistral Medium',
            'mistral-small-latest': 'Mistral Small',
            'deepseek-chat': 'DeepSeek Chat',
            'deepseek-coder': 'DeepSeek Coder',
            'gpt-4o': 'GPT-4o',
            'gpt-4-turbo': 'GPT-4 Turbo',
            'gpt-3.5-turbo': 'GPT-3.5 Turbo',
            'claude-3-5-sonnet-20241022': 'Claude 3.5 Sonnet',
            'claude-3-opus-20240229': 'Claude 3 Opus',
            'claude-3-haiku-20240307': 'Claude 3 Haiku'
        };
        
        return modelMap[model] || model;
    };

    const getProviderFromModel = (model: string) => {
        if (model.startsWith('gemini')) return 'Google';
        if (model.startsWith('mistral')) return 'Mistral';
        if (model.startsWith('deepseek')) return 'DeepSeek';
        if (model.startsWith('gpt')) return 'OpenAI';
        if (model.startsWith('claude')) return 'Anthropic';
        return 'Unknown';
    };

    const groupedModels = availableModels.reduce((acc, model) => {
        const provider = getProviderFromModel(model);
        if (!acc[provider]) {
            acc[provider] = [];
        }
        acc[provider].push(model);
        return acc;
    }, {} as { [provider: string]: string[] });

    return (
        <div className="model-selector">
            <span>Model:</span>
            <select
                className="model-dropdown"
                value={currentModel}
                onChange={(e) => onModelChange(e.target.value)}
            >
                {Object.entries(groupedModels).map(([provider, models]) => (
                    <optgroup key={provider} label={provider}>
                        {models.map(model => (
                            <option key={model} value={model}>
                                {getModelDisplayName(model)}
                            </option>
                        ))}
                    </optgroup>
                ))}
            </select>
            <span style={{ opacity: 0.7, fontSize: '11px' }}>
                Provider: {getProviderFromModel(currentModel)}
            </span>
        </div>
    );
};
