import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import fetch from 'node-fetch';

export interface ContextItem {
    type: 'file' | 'directory' | 'url' | 'selection' | 'workspace';
    path: string;
    content?: string;
    metadata?: any;
}

export class ContextManager {
    private context: vscode.ExtensionContext;
    private contextItems: Map<string, ContextItem> = new Map();

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
    }

    /**
     * Parse @ mentions from user input and resolve them to context items
     */
    async parseContextMentions(input: string): Promise<{ cleanInput: string; contextItems: ContextItem[] }> {
        const mentionRegex = /@([^\s]+)/g;
        const mentions: string[] = [];
        let match;

        while ((match = mentionRegex.exec(input)) !== null) {
            mentions.push(match[1]);
        }

        const contextItems: ContextItem[] = [];
        let cleanInput = input;

        for (const mention of mentions) {
            const contextItem = await this.resolveContextMention(mention);
            if (contextItem) {
                contextItems.push(contextItem);
                // Remove the mention from the input
                cleanInput = cleanInput.replace(new RegExp(`@${mention}\\b`, 'g'), '').trim();
            }
        }

        return { cleanInput, contextItems };
    }

    /**
     * Resolve a context mention to a context item
     */
    private async resolveContextMention(mention: string): Promise<ContextItem | null> {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            return null;
        }

        // Check if it's a URL
        if (mention.startsWith('http://') || mention.startsWith('https://')) {
            return await this.createUrlContextItem(mention);
        }

        // Check if it's a file or directory path
        const fullPath = path.isAbsolute(mention) ? mention : path.join(workspaceRoot, mention);
        
        try {
            const stat = fs.statSync(fullPath);
            
            if (stat.isFile()) {
                return await this.createFileContextItem(fullPath);
            } else if (stat.isDirectory()) {
                return await this.createDirectoryContextItem(fullPath);
            }
        } catch (error) {
            // Path doesn't exist, try fuzzy matching
            return await this.fuzzyMatchPath(mention, workspaceRoot);
        }

        return null;
    }

    /**
     * Create context item for a file
     */
    private async createFileContextItem(filePath: string): Promise<ContextItem> {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const relativePath = vscode.workspace.asRelativePath(filePath);
            
            return {
                type: 'file',
                path: relativePath,
                content: content,
                metadata: {
                    size: content.length,
                    extension: path.extname(filePath),
                    lastModified: fs.statSync(filePath).mtime
                }
            };
        } catch (error) {
            throw new Error(`Failed to read file: ${filePath}`);
        }
    }

    /**
     * Create context item for a directory
     */
    private async createDirectoryContextItem(dirPath: string): Promise<ContextItem> {
        try {
            const files = fs.readdirSync(dirPath);
            const relativePath = vscode.workspace.asRelativePath(dirPath);
            
            // Get directory structure
            const structure = await this.getDirectoryStructure(dirPath, 2); // Max 2 levels deep
            
            return {
                type: 'directory',
                path: relativePath,
                content: JSON.stringify(structure, null, 2),
                metadata: {
                    fileCount: files.length,
                    structure: structure
                }
            };
        } catch (error) {
            throw new Error(`Failed to read directory: ${dirPath}`);
        }
    }

    /**
     * Create context item for a URL
     */
    private async createUrlContextItem(url: string): Promise<ContextItem> {
        try {
            const response = await fetch(url);
            const content = await response.text();
            
            // Extract title and clean content
            const title = this.extractTitle(content);
            const cleanContent = this.extractTextContent(content);
            
            return {
                type: 'url',
                path: url,
                content: cleanContent.substring(0, 10000), // Limit content
                metadata: {
                    title: title,
                    status: response.status,
                    contentType: response.headers.get('content-type'),
                    contentLength: cleanContent.length
                }
            };
        } catch (error) {
            throw new Error(`Failed to fetch URL: ${url}`);
        }
    }

    /**
     * Fuzzy match a path against workspace files
     */
    private async fuzzyMatchPath(mention: string, workspaceRoot: string): Promise<ContextItem | null> {
        try {
            // Use VS Code's workspace.findFiles to search for matching files
            const pattern = `**/*${mention}*`;
            const files = await vscode.workspace.findFiles(pattern, null, 10);
            
            if (files.length > 0) {
                // Return the first match
                const bestMatch = files[0];
                return await this.createFileContextItem(bestMatch.fsPath);
            }
        } catch (error) {
            console.error('Error in fuzzy matching:', error);
        }
        
        return null;
    }

    /**
     * Get directory structure recursively
     */
    private async getDirectoryStructure(dirPath: string, maxDepth: number, currentDepth: number = 0): Promise<any> {
        if (currentDepth >= maxDepth) {
            return '...';
        }

        try {
            const items = fs.readdirSync(dirPath);
            const structure: any = {};

            for (const item of items) {
                const itemPath = path.join(dirPath, item);
                const stat = fs.statSync(itemPath);

                if (stat.isDirectory()) {
                    structure[item + '/'] = await this.getDirectoryStructure(itemPath, maxDepth, currentDepth + 1);
                } else {
                    structure[item] = {
                        size: stat.size,
                        modified: stat.mtime
                    };
                }
            }

            return structure;
        } catch (error) {
            return { error: 'Access denied' };
        }
    }

    /**
     * Extract title from HTML content
     */
    private extractTitle(htmlContent: string): string {
        const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i);
        return titleMatch ? titleMatch[1].trim() : 'No title';
    }

    /**
     * Extract text content from HTML
     */
    private extractTextContent(htmlContent: string): string {
        // Remove script and style elements
        let content = htmlContent.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
        content = content.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
        
        // Remove HTML tags
        content = content.replace(/<[^>]*>/g, ' ');
        
        // Clean up whitespace
        content = content.replace(/\s+/g, ' ').trim();
        
        return content;
    }

    /**
     * Get current editor selection as context
     */
    getCurrentSelection(): ContextItem | null {
        const editor = vscode.window.activeTextEditor;
        if (!editor || editor.selection.isEmpty) {
            return null;
        }

        const selection = editor.document.getText(editor.selection);
        const filePath = vscode.workspace.asRelativePath(editor.document.uri);

        return {
            type: 'selection',
            path: filePath,
            content: selection,
            metadata: {
                startLine: editor.selection.start.line + 1,
                endLine: editor.selection.end.line + 1,
                startChar: editor.selection.start.character,
                endChar: editor.selection.end.character
            }
        };
    }

    /**
     * Format context items for inclusion in prompts
     */
    formatContextForPrompt(contextItems: ContextItem[]): string {
        if (contextItems.length === 0) {
            return '';
        }

        let contextString = '\n\n**CONTEXT PROVIDED:**\n';
        
        for (const item of contextItems) {
            contextString += `\n### ${item.type.toUpperCase()}: ${item.path}\n`;
            
            if (item.metadata) {
                if (item.type === 'file') {
                    contextString += `*File size: ${item.metadata.size} characters, Extension: ${item.metadata.extension}*\n`;
                } else if (item.type === 'url') {
                    contextString += `*Title: ${item.metadata.title}, Status: ${item.metadata.status}*\n`;
                } else if (item.type === 'selection') {
                    contextString += `*Lines ${item.metadata.startLine}-${item.metadata.endLine}*\n`;
                }
            }
            
            if (item.content) {
                contextString += '```\n' + item.content + '\n```\n';
            }
        }

        return contextString;
    }

    /**
     * Cache context item for reuse
     */
    cacheContextItem(key: string, item: ContextItem): void {
        this.contextItems.set(key, item);
    }

    /**
     * Get cached context item
     */
    getCachedContextItem(key: string): ContextItem | undefined {
        return this.contextItems.get(key);
    }

    /**
     * Clear context cache
     */
    clearCache(): void {
        this.contextItems.clear();
    }
}
