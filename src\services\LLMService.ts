import { GoogleGenerativeAI, GenerativeModel, GenerateContentResult } from '@google/generative-ai';
import { ApiKeyManager } from '../managers/ApiKeyManager';
import { ToolDefinition } from '../types/tools';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './ErrorHandler';
import axios from 'axios';

export interface LLMProvider {
  name: string;
  models: string[];
  defaultModel: string;
  apiEndpoint?: string;
}

export const SUPPORTED_PROVIDERS: { [key: string]: LLMProvider } = {
  gemini: {
    name: 'Google Gemini',
    models: ['gemini-2.0-flash', 'gemini-2.5-flash',  'gemini-1.5-flash'],
    defaultModel: 'gemini-2.0-flas'
  },
  mistral: {
    name: 'Mistral AI',
    models: ['mistral-large-latest', 'mistral-medium-latest', 'mistral-small-latest'],
    defaultModel: 'mistral-large-latest',
    apiEndpoint: 'https://api.mistral.ai/v1'
  },
  deepseek: {
    name: 'DeepSeek',
    models: ['deepseek-chat', 'deepseek-coder'],
    defaultModel: 'deepseek-coder',
    apiEndpoint: 'https://api.deepseek.com/v1'
  },
  openai: {
    name: 'OpenAI',
    models: ['gpt-4o', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    defaultModel: 'gpt-4o',
    apiEndpoint: 'https://api.openai.com/v1'
  },
  anthropic: {
    name: 'Anthropic',
    models: ['claude-3-5-sonnet-20241022', 'claude-3-opus-20240229', 'claude-3-haiku-20240307'],
    defaultModel: 'claude-3-5-sonnet-20241022',
    apiEndpoint: 'https://api.anthropic.com/v1'
  }
};

export class LLMService {
    private apiKeyManager: ApiKeyManager;
    private currentProvider: string = 'gemini';
    private currentModel: string = 'gemini-2.0-flash-exp';
    private geminiClient?: GoogleGenerativeAI;
    private errorHandler: ErrorHandler;

    constructor(apiKeyManager: ApiKeyManager) {
        this.apiKeyManager = apiKeyManager;
        this.errorHandler = ErrorHandler.getInstance();
    }

    /**
     * Initialize the service with API keys
     */
    async initialize(): Promise<void> {
        const geminiKey = await this.apiKeyManager.getApiKey('gemini');
        if (geminiKey) {
            this.geminiClient = new GoogleGenerativeAI(geminiKey);
        }
    }

    /**
     * Set current model and provider
     */
    async setModel(model: string): Promise<void> {
        // Find which provider this model belongs to
        for (const [provider, config] of Object.entries(SUPPORTED_PROVIDERS)) {
            if (config.models.includes(model)) {
                this.currentProvider = provider;
                this.currentModel = model;
                
                // Initialize client if needed
                if (provider === 'gemini' && !this.geminiClient) {
                    const key = await this.apiKeyManager.getApiKey('gemini');
                    if (key) {
                        this.geminiClient = new GoogleGenerativeAI(key);
                    }
                }
                return;
            }
        }
        throw new Error(`Model ${model} not found in any provider`);
    }

    /**
     * Get current model info
     */
    getCurrentModel(): { provider: string; model: string } {
        return {
            provider: this.currentProvider,
            model: this.currentModel
        };
    }

    /**
     * Get available models
     */
    async getAvailableModels(): Promise<{ provider: string; models: string[] }[]> {
        const available: { provider: string; models: string[] }[] = [];
        
        for (const [provider, config] of Object.entries(SUPPORTED_PROVIDERS)) {
            const hasKey = await this.apiKeyManager.hasApiKey(provider);
            if (hasKey) {
                available.push({
                    provider: config.name,
                    models: config.models
                });
            }
        }
        
        return available;
    }

    /**
     * Generate content using the current model
     */
    async generateContent(
        prompt: string | any[], 
        tools?: ToolDefinition[],
        systemPrompt?: string
    ): Promise<GenerateContentResult> {
        if (this.currentProvider === 'gemini') {
            return this.generateWithGemini(prompt, tools, systemPrompt);
        } else {
            return this.generateWithGenericProvider(prompt, tools, systemPrompt);
        }
    }

    /**
     * Generate content with Gemini
     */
    private async generateWithGemini(
        prompt: string | any[], 
        tools?: ToolDefinition[],
        systemPrompt?: string
    ): Promise<GenerateContentResult> {
        if (!this.geminiClient) {
            throw new Error('Gemini client not initialized');
        }

        const model = this.geminiClient.getGenerativeModel({ 
            model: this.currentModel,
            systemInstruction: systemPrompt
        });

        const generationConfig = {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 8192,
        };

        if (tools && tools.length > 0) {
            const geminiTools = tools.map(tool => ({
                functionDeclarations: [{
                    name: tool.name,
                    description: tool.description,
                    parameters: {
                        type: 'OBJECT' as any,
                        properties: tool.parameters.properties,
                        required: tool.parameters.required
                    }
                }]
            }));

            const result = await model.generateContent({
                contents: [{ role: 'user', parts: [{ text: typeof prompt === 'string' ? prompt : JSON.stringify(prompt) }] }],
                tools: geminiTools,
                generationConfig
            });

            return result;
        } else {
            const result = await model.generateContent({
                contents: [{ role: 'user', parts: [{ text: typeof prompt === 'string' ? prompt : JSON.stringify(prompt) }] }],
                generationConfig
            });

            return result;
        }
    }

    /**
     * Generate content with other providers (OpenAI-compatible API)
     */
    private async generateWithGenericProvider(
        prompt: string | any[], 
        tools?: ToolDefinition[],
        systemPrompt?: string
    ): Promise<GenerateContentResult> {
        const provider = SUPPORTED_PROVIDERS[this.currentProvider];
        if (!provider.apiEndpoint) {
            throw new Error(`No API endpoint configured for provider: ${this.currentProvider}`);
        }

        const apiKey = await this.apiKeyManager.getApiKey(this.currentProvider);
        if (!apiKey) {
            throw new Error(`No API key found for provider: ${this.currentProvider}`);
        }

        const messages: any[] = [];
        if (systemPrompt) {
            messages.push({ role: 'system', content: systemPrompt });
        }
        messages.push({ role: 'user', content: typeof prompt === 'string' ? prompt : JSON.stringify(prompt) });

        const requestBody: any = {
            model: this.currentModel,
            messages,
            temperature: 0.7,
            max_tokens: 8192
        };

        if (tools && tools.length > 0) {
            requestBody.tools = tools.map(tool => ({
                type: 'function',
                function: {
                    name: tool.name,
                    description: tool.description,
                    parameters: tool.parameters
                }
            }));
        }

        try {
            const response = await axios.post(
                `${provider.apiEndpoint}/chat/completions`,
                requestBody,
                {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            // Convert to Gemini-compatible format
            const choice = response.data.choices[0];
            return {
                response: {
                    text: () => choice.message.content || '',
                    functionCalls: () => choice.message.tool_calls || []
                }
            } as GenerateContentResult;

        } catch (error: any) {
            const errorInfo = this.errorHandler.handleApiError(error, this.currentProvider);
            console.error(`Error calling ${this.currentProvider} API:`, error);
            throw new Error(errorInfo.message);
        }
    }

    /**
     * Stream content generation (for auto-agent mode)
     */
    async *streamContent(
        prompt: string | any[],
        tools?: ToolDefinition[],
        systemPrompt?: string
    ): AsyncGenerator<GenerateContentResult> {
        // For now, just yield the complete result
        // TODO: Implement proper streaming for all providers
        const result = await this.generateContent(prompt, tools, systemPrompt);
        yield result;
    }

    /**
     * Test API key for a provider
     */
    async testApiKey(provider: string): Promise<boolean> {
        try {
            const originalProvider = this.currentProvider;
            const originalModel = this.currentModel;

            // Temporarily switch to test provider
            const providerConfig = SUPPORTED_PROVIDERS[provider];
            if (!providerConfig) {
                return false;
            }

            await this.setModel(providerConfig.defaultModel);

            // Test with a simple prompt
            const result = await this.generateContent('Hello, respond with just "OK"');

            // Restore original settings
            this.currentProvider = originalProvider;
            this.currentModel = originalModel;

            return !!result;
        } catch (error) {
            console.error(`API key test failed for ${provider}:`, error);
            return false;
        }
    }
}
