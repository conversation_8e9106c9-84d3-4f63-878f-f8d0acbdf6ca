import * as vscode from 'vscode';
import { LLMService } from '../services/LLMService';
import { ToolController } from '../controllers/ToolController';
import { WebviewPanelProvider } from '../providers/WebviewPanelProvider';
import { SidePanelProvider } from '../providers/SidePanelProvider';
import { SystemPrompts } from '../services/SystemPrompts';
import { ContextManager } from '../services/ContextManager';
import {
    WebviewToExtensionMessage,
    ExtensionToWebviewMessage,
    AgentState,
    Plan,
    PlanStep,
    ChatMessage
} from '../types/messaging';
import { AgentContext, AgentMemory, ProjectInfo } from '../types/agent';

export class AgentExecutor {
    private llmService: LLMService;
    private toolController: ToolController;
    private webviewProvider: WebviewPanelProvider | SidePanelProvider;
    private contextManager: ContextManager;
    private currentState: AgentState = AgentState.IDLE;
    private currentPlan?: Plan;
    private context: AgentContext;
    private memory: AgentMemory;
    private conversationHistory: ChatMessage[] = [];
    private projectInfo?: ProjectInfo;

    constructor(
        llmService: LLMService,
        toolController: ToolController,
        webviewProvider: WebviewPanelProvider | SidePanelProvider,
        extensionContext: vscode.ExtensionContext
    ) {
        this.llmService = llmService;
        this.toolController = toolController;
        this.webviewProvider = webviewProvider;
        this.contextManager = new ContextManager(extensionContext);
        
        // Initialize context and memory
        this.context = {
            currentMode: 'chat',
            currentModel: 'gemini-2.0-flash-exp',
            conversationHistory: [],
            activeFiles: [],
            recentErrors: []
        };

        this.memory = {
            shortTerm: {
                recentActions: [],
                contextFiles: [],
                errors: []
            },
            longTerm: {
                userPreferences: {},
                projectPatterns: {},
                successfulStrategies: []
            }
        };

        // Set up tool controller reference
        this.toolController.setWebviewProvider(this.webviewProvider);
    }

    /**
     * Handle messages from the webview
     */
    async handleWebviewMessage(message: WebviewToExtensionMessage): Promise<void> {
        try {
            switch (message.type) {
                case 'userQuery':
                    const query = message.query || message.payload || '';
                    await this.handleUserQuery(query, message.mode || 'chat', message.model);
                    break;
                case 'newChat':
                    await this.newChat();
                    break;
                case 'setApiKey':
                    await this.handleSetApiKey(message.payload);
                    break;
                case 'getApiKeys':
                    await this.handleGetApiKeys();
                    break;
                case 'getCurrentModel':
                    await this.handleGetCurrentModel();
                    break;
                case 'getModels':
                    await this.handleGetModels();
                    break;
                case 'setModel':
                    await this.handleSetModel(message.payload);
                    break;
                case 'modeChanged':
                    await this.handleModeChanged(message.mode);
                    break;
                case 'tabChanged':
                    await this.handleTabChanged(message.tab);
                    break;
                case 'approvePlan':
                    await this.handlePlanApproval(message.payload.planId);
                    break;
                case 'acceptDiff':
                    await this.handleDiffApproval(message.payload.diffId, true);
                    break;
                case 'rejectDiff':
                    await this.handleDiffApproval(message.payload.diffId, false);
                    break;
                case 'userInput':
                    if (message.payload && 'questionId' in message.payload && 'response' in message.payload) {
                        await this.handleUserInput(message.payload.questionId, message.payload.response);
                    }
                    break;
                case 'stopAgent':
                    await this.stopAgent();
                    break;
                case 'newChat':
                    await this.newChat();
                    break;
                case 'setApiKey':
                    await this.handleSetApiKey(message.payload);
                    break;
                case 'getApiKeys':
                    await this.handleGetApiKeys();
                    break;
                case 'setModel':
                    await this.handleSetModel(message.payload);
                    break;
                case 'getModels':
                    await this.handleGetModels();
                    break;
                default:
                    console.warn('Unknown message type:', (message as any).type);
            }
        } catch (error: any) {
            console.error('Error handling webview message:', error);
            this.sendErrorToWebview(error.message);
        }
    }

    /**
     * Handle user query based on mode
     */
    private async handleUserQuery(query: string, mode: 'chat' | 'agent' | 'auto-agent', model?: string): Promise<void> {
        this.context.currentMode = mode;
        if (model) {
            this.context.currentModel = model;
            await this.llmService.setModel(model);
        }

        // Parse context mentions (@file, @url, etc.)
        const { cleanInput, contextItems } = await this.contextManager.parseContextMentions(query);

        // Add current selection if available
        const currentSelection = this.contextManager.getCurrentSelection();
        if (currentSelection) {
            contextItems.push(currentSelection);
        }

        // Format context for prompt
        const contextString = this.contextManager.formatContextForPrompt(contextItems);
        const enhancedQuery = cleanInput + contextString;

        // Add user message to conversation history
        const userMessage: ChatMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: query, // Keep original query for display
            timestamp: Date.now(),
            mode
        };
        this.conversationHistory.push(userMessage);

        switch (mode) {
            case 'chat':
                await this.handleChatMode(enhancedQuery);
                break;
            case 'agent':
                await this.handleAgentMode(enhancedQuery);
                break;
            case 'auto-agent':
                await this.handleAutoAgentMode(enhancedQuery);
                break;
        }
    }

    /**
     * Handle chat mode - read-only interactions
     */
    private async handleChatMode(query: string): Promise<void> {
        try {
            this.updateState(AgentState.PLANNING);

            // Get read-only tools
            const readOnlyTools = this.toolController.getToolDefinitions().filter(tool => 
                ['readFile', 'readDirectory', 'getCodebaseAST', 'findSymbolDefinition', 'getProjectInfo', 'webSearch'].includes(tool.name)
            );

            const systemPrompt = await this.getChatModeSystemPrompt();
            const result = await this.llmService.generateContent(query, readOnlyTools, systemPrompt);
            
            const response = result.response.text();
            
            // Add assistant response to conversation history
            const assistantMessage: ChatMessage = {
                id: Date.now().toString(),
                role: 'assistant',
                content: response,
                timestamp: Date.now(),
                mode: 'chat'
            };
            this.conversationHistory.push(assistantMessage);

            // Send response to webview
            this.webviewProvider.sendMessage({
                type: 'aiResponse',
                payload: response,
                mode: 'chat'
            });

            this.updateState(AgentState.IDLE);
        } catch (error: any) {
            this.sendErrorToWebview(`Chat mode error: ${error.message}`);
            this.updateState(AgentState.ERROR);
        }
    }

    /**
     * Handle agent mode - supervised task execution
     */
    private async handleAgentMode(query: string): Promise<void> {
        try {
            this.updateState(AgentState.PLANNING);

            // Generate plan
            const plan = await this.generatePlan(query);
            this.currentPlan = plan;

            // Send plan to webview for approval
            this.webviewProvider.sendMessage({
                type: 'planGenerated',
                payload: plan,
                mode: 'agent'
            });

            this.updateState(AgentState.AWAITING_PLAN_APPROVAL);
        } catch (error: any) {
            this.sendErrorToWebview(`Agent mode error: ${error.message}`);
            this.updateState(AgentState.ERROR);
        }
    }

    /**
     * Handle auto-agent mode - autonomous execution
     */
    private async handleAutoAgentMode(query: string): Promise<void> {
        try {
            this.updateState(AgentState.EXECUTING_STEP);

            // Stream thoughts and actions
            const systemPrompt = await this.getAutoAgentSystemPrompt();
            const tools = this.toolController.getToolDefinitions();

            // Start autonomous execution loop
            await this.autonomousExecutionLoop(query, systemPrompt, tools);

        } catch (error: any) {
            this.sendErrorToWebview(`Auto-agent mode error: ${error.message}`);
            this.updateState(AgentState.ERROR);
        }
    }

    /**
     * Generate a plan for agent mode
     */
    private async generatePlan(query: string): Promise<Plan> {
        const systemPrompt = await this.getAgentModeSystemPrompt();
        const tools = this.toolController.getToolDefinitions();
        
        const planningPrompt = `
        Create a detailed plan to accomplish this task: "${query}"
        
        Available tools: ${tools.map(t => t.name).join(', ')}
        
        Return a JSON plan with the following structure:
        {
            "description": "Brief description of what will be accomplished",
            "steps": [
                {
                    "description": "What this step will do",
                    "toolCall": {
                        "tool": "toolName",
                        "parameters": {...}
                    }
                }
            ]
        }
        `;

        const result = await this.llmService.generateContent(planningPrompt, [], systemPrompt);
        const response = result.response.text();

        try {
            const planData = JSON.parse(response);
            const plan: Plan = {
                id: `plan_${Date.now()}`,
                description: planData.description,
                status: 'pending',
                steps: planData.steps.map((step: any, index: number) => ({
                    id: `step_${index}`,
                    description: step.description,
                    status: 'pending',
                    toolCall: step.toolCall
                }))
            };
            return plan;
        } catch (error) {
            throw new Error(`Failed to parse plan: ${error}`);
        }
    }

    /**
     * Autonomous execution loop for auto-agent mode
     */
    private async autonomousExecutionLoop(query: string, systemPrompt: string, tools: any[]): Promise<void> {
        let iterations = 0;
        const maxIterations = 10;

        while (iterations < maxIterations && this.currentState === AgentState.EXECUTING_STEP) {
            try {
                // Send thought to webview
                this.webviewProvider.sendMessage({
                    type: 'agentThought',
                    payload: `Iteration ${iterations + 1}: Analyzing task and determining next action...`,
                    mode: 'auto-agent'
                });

                const result = await this.llmService.generateContent(
                    `Task: ${query}\nIteration: ${iterations + 1}\nDetermine the next action to take.`,
                    tools,
                    systemPrompt
                );

                const functionCalls = result.response.functionCalls();
                if (functionCalls && functionCalls.length > 0) {
                    for (const call of functionCalls) {
                        // Send action to webview
                        this.webviewProvider.sendMessage({
                            type: 'agentAction',
                            payload: { tool: call.name, params: call.args },
                            mode: 'auto-agent'
                        });

                        // Execute tool
                        const toolResult = await this.toolController.executeTool(call.name, call.args);
                        
                        // Send result to webview
                        this.webviewProvider.sendMessage({
                            type: 'agentResult',
                            payload: toolResult.success ? JSON.stringify(toolResult.data) : toolResult.error || 'Unknown error',
                            mode: 'auto-agent'
                        });

                        if (!toolResult.success) {
                            this.webviewProvider.sendMessage({
                                type: 'agentError',
                                payload: toolResult.error || 'Tool execution failed',
                                mode: 'auto-agent'
                            });
                        }
                    }
                } else {
                    // No more actions needed
                    break;
                }

                iterations++;
            } catch (error: any) {
                this.webviewProvider.sendMessage({
                    type: 'agentError',
                    payload: error.message,
                    mode: 'auto-agent'
                });
                break;
            }
        }

        this.updateState(AgentState.COMPLETED);
    }

    /**
     * Handle plan approval
     */
    private async handlePlanApproval(planId: string): Promise<void> {
        if (!this.currentPlan || this.currentPlan.id !== planId) {
            this.sendErrorToWebview('Plan not found or expired');
            return;
        }

        try {
            this.currentPlan.status = 'executing';
            this.updateState(AgentState.EXECUTING_STEP);

            // Execute plan steps sequentially
            for (const step of this.currentPlan.steps) {
                if (this.currentState !== AgentState.EXECUTING_STEP) {
                    break; // User stopped execution
                }

                step.status = 'executing';
                this.sendPlanStepUpdate(step);

                if (step.toolCall) {
                    const result = await this.toolController.executeTool(
                        step.toolCall.tool,
                        step.toolCall.parameters
                    );

                    if (result.success) {
                        step.status = 'completed';
                        step.result = typeof result.data === 'string' ? result.data : JSON.stringify(result.data);
                    } else {
                        step.status = 'failed';
                        step.error = result.error;
                        this.sendErrorToWebview(`Step failed: ${result.error}`);
                        break;
                    }
                } else {
                    step.status = 'completed';
                }

                this.sendPlanStepUpdate(step);
            }

            this.currentPlan.status = 'completed';
            this.updateState(AgentState.COMPLETED);
        } catch (error: any) {
            this.sendErrorToWebview(`Plan execution error: ${error.message}`);
            this.updateState(AgentState.ERROR);
        }
    }

    /**
     * Handle diff approval/rejection
     */
    private async handleDiffApproval(diffId: string, approved: boolean): Promise<void> {
        // This would be handled by the ToolController
        // For now, just acknowledge the response
        console.log(`Diff ${diffId} ${approved ? 'approved' : 'rejected'}`);
    }

    /**
     * Handle user input response
     */
    private async handleUserInput(questionId: string, response: string): Promise<void> {
        // This would be handled by the ToolController
        console.log(`Question ${questionId} answered: ${response}`);
    }

    /**
     * Stop agent execution
     */
    private async stopAgent(): Promise<void> {
        this.updateState(AgentState.STOPPED);
        if (this.currentPlan) {
            this.currentPlan.status = 'failed';
        }
    }

    /**
     * Start new chat
     */
    private async newChat(): Promise<void> {
        this.conversationHistory = [];
        this.currentPlan = undefined;
        this.updateState(AgentState.IDLE);

        // Clear memory
        this.memory.shortTerm = {
            recentActions: [],
            contextFiles: [],
            errors: []
        };
    }

    /**
     * Handle API key setting
     */
    private async handleSetApiKey(payload: { provider: string; key: string }): Promise<void> {
        const { provider, key } = payload;
        try {
            // Get the API key manager from the extension context
            const apiKeyManager = (this.webviewProvider as any).apiKeyManager;
            if (apiKeyManager) {
                await apiKeyManager.setApiKey(provider, key);

                // Test the API key if it's for the current LLM service
                if (provider === 'gemini') {
                    await this.llmService.initialize();
                }

                // Send success confirmation
                this.webviewProvider.sendMessage({
                    type: 'apiKeys',
                    payload: await apiKeyManager.getApiKeyStatus(),
                    mode: 'all'
                });
            }
        } catch (error: any) {
            this.sendErrorToWebview(`Failed to set API key: ${error.message}`);
        }
    }

    /**
     * Handle get API keys request
     */
    private async handleGetApiKeys(): Promise<void> {
        try {
            const apiKeyManager = (this.webviewProvider as any).apiKeyManager;
            if (apiKeyManager) {
                const apiKeyStatus = await apiKeyManager.getApiKeyStatus();

                this.webviewProvider.sendMessage({
                    type: 'apiKeys',
                    payload: apiKeyStatus,
                    mode: 'all'
                });
            } else {
                // Fallback if no API key manager
                const apiKeys = {
                    gemini: false,
                    openai: false,
                    anthropic: false,
                    mistral: false,
                    deepseek: false
                };

                this.webviewProvider.sendMessage({
                    type: 'apiKeys',
                    payload: apiKeys,
                    mode: 'all'
                });
            }
        } catch (error: any) {
            this.sendErrorToWebview(`Failed to get API keys: ${error.message}`);
        }
    }

    /**
     * Handle model setting
     */
    private async handleSetModel(payload: { provider: string; model: string }): Promise<void> {
        try {
            // Set the model (which will also set the provider)
            await this.llmService.setModel(payload.model);
            this.context.currentModel = payload.model;

            // Send confirmation with current model info
            const currentModel = this.llmService.getCurrentModel();
            this.webviewProvider.sendMessage({
                type: 'currentModel',
                payload: currentModel
            });
        } catch (error: any) {
            this.sendErrorToWebview(`Failed to set model: ${error.message}`);
        }
    }

    /**
     * Handle get models request
     */
    private async handleGetModels(): Promise<void> {
        try {
            const availableModels = await this.llmService.getAvailableModels();
            const current = this.llmService.getCurrentModel();

            this.webviewProvider.sendMessage({
                type: 'models',
                payload: {
                    available: availableModels.flatMap(provider => provider.models),
                    current: current.model
                },
                mode: 'all'
            });
        } catch (error: any) {
            this.sendErrorToWebview(`Failed to get models: ${error.message}`);
        }
    }

    /**
     * Update agent state and notify webview
     */
    private updateState(newState: AgentState): void {
        this.currentState = newState;
        this.webviewProvider.sendMessage({
            type: 'agentStatus',
            payload: newState,
            mode: 'all'
        });
    }

    /**
     * Send plan step update to webview
     */
    private sendPlanStepUpdate(step: PlanStep): void {
        this.webviewProvider.sendMessage({
            type: 'planStepUpdate',
            payload: step,
            mode: 'agent'
        });
    }

    /**
     * Send error message to webview
     */
    private sendErrorToWebview(error: string): void {
        this.webviewProvider.sendMessage({
            type: 'agentError',
            payload: error,
            mode: 'all'
        });
    }

    /**
     * Get system prompt for chat mode
     */
    private async getChatModeSystemPrompt(): Promise<string> {
        // Get project info for context
        if (!this.projectInfo) {
            const projectResult = await this.toolController.executeTool('getProjectInfo', {});
            if (projectResult.success) {
                this.projectInfo = projectResult.data;
            }
        }

        return SystemPrompts.getContextualPrompt(this.projectInfo, 'chat', this.context);
    }

    /**
     * Get system prompt for agent mode
     */
    private async getAgentModeSystemPrompt(): Promise<string> {
        if (!this.projectInfo) {
            const projectResult = await this.toolController.executeTool('getProjectInfo', {});
            if (projectResult.success) {
                this.projectInfo = projectResult.data;
            }
        }

        return SystemPrompts.getContextualPrompt(this.projectInfo, 'agent', this.context);
    }

    /**
     * Get system prompt for auto-agent mode
     */
    private async getAutoAgentSystemPrompt(): Promise<string> {
        if (!this.projectInfo) {
            const projectResult = await this.toolController.executeTool('getProjectInfo', {});
            if (projectResult.success) {
                this.projectInfo = projectResult.data;
            }
        }

        return SystemPrompts.getContextualPrompt(this.projectInfo, 'auto-agent', this.context);
    }

    /**
     * Send message to webview
     */
    private sendToWebview(message: ExtensionToWebviewMessage): void {
        this.webviewProvider.sendMessage(message);
    }



    /**
     * Handle get current model request
     */
    private async handleGetCurrentModel(): Promise<void> {
        try {
            const currentModel = this.llmService.getCurrentModel();
            this.webviewProvider.sendMessage({
                type: 'currentModel',
                payload: currentModel
            });
        } catch (error: any) {
            this.sendErrorToWebview(`Failed to get current model: ${error.message}`);
        }
    }

    /**
     * Handle mode changed
     */
    private async handleModeChanged(mode: string): Promise<void> {
        this.context.currentMode = mode as 'chat' | 'agent' | 'auto-agent';
    }

    /**
     * Handle tab changed
     */
    private async handleTabChanged(tab: string): Promise<void> {
        // Handle tab-specific logic if needed
        console.log(`Tab changed to: ${tab}`);
    }
}
