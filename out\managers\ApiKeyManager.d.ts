import * as vscode from 'vscode';
export declare class ApiKeyManager {
    private context;
    private readonly secretStorage;
    constructor(context: vscode.ExtensionContext);
    /**
     * Get API key for a specific provider
     */
    getApiKey(provider: string): Promise<string | undefined>;
    /**
     * Set API key for a specific provider
     */
    setApiKey(provider: string, key: string): Promise<void>;
    /**
     * Remove API key for a specific provider
     */
    removeApiKey(provider: string): Promise<void>;
    /**
     * Check if API key exists for a provider
     */
    hasApiKey(provider: string): Promise<boolean>;
    /**
     * Get all configured providers
     */
    getConfiguredProviders(): Promise<string[]>;
    /**
     * Prompt user for API key
     */
    promptForApiKey(provider: string): Promise<string | undefined>;
    /**
     * Get API key status for all providers
     */
    getApiKeyStatus(): Promise<{
        [provider: string]: boolean;
    }>;
    /**
     * Validate API key format (basic validation)
     */
    validateApiKeyFormat(provider: string, key: string): boolean;
}
//# sourceMappingURL=ApiKeyManager.d.ts.map