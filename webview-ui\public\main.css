/* Main application styles */
#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
  color: #cccccc;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #252526;
  border-bottom: 1px solid #3c3c3c;
  min-height: 48px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title {
  font-weight: 400;
  font-size: 13px;
  color: #8c8c8c;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.new-chat-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.new-chat-dropdown {
  background-color: #3c3c3c;
  color: #cccccc;
  border: 1px solid #464647;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  min-width: 120px;
  cursor: pointer;
}

.new-chat-dropdown:hover {
  background-color: #404040;
}

.new-chat-btn {
  background-color: #007acc;
  color: #ffffff;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
  transition: background-color 0.2s;
}

.new-chat-btn:hover {
  background-color: #1177bb;
}

.settings-btn {
  background: none;
  border: none;
  color: #cccccc;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.settings-btn:hover {
  background-color: #2a2d2e;
}

/* Main content area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-area {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #cccccc;
  opacity: 0.7;
  font-size: 14px;
  text-align: center;
}

.empty-state h3 {
  color: #cccccc;
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 500;
}

.empty-state p {
  color: #8c8c8c;
  margin: 4px 0;
}

/* Chat messages */
.message {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 100%;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--vscode-sideBar-foreground);
  opacity: 0.8;
}

.message-role {
  font-weight: 600;
  text-transform: uppercase;
}

.message-timestamp {
  opacity: 0.6;
}

.message-content {
  background-color: var(--vscode-editorWidget-background);
  border: 1px solid var(--vscode-editorWidget-border);
  border-radius: 6px;
  padding: 12px;
  line-height: 1.5;
}

.message-content.user {
  background-color: var(--vscode-input-background);
  border-color: var(--vscode-input-border);
}

.message-content.assistant {
  background-color: var(--vscode-editorWidget-background);
  border-color: var(--vscode-editorWidget-border);
}

/* Code blocks in messages */
.message-content pre {
  position: relative;
  margin: 8px 0;
}

.code-copy-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  padding: 4px 8px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 11px;
  opacity: 0.8;
}

.code-copy-btn:hover {
  opacity: 1;
  background-color: var(--vscode-button-hoverBackground);
}

/* Bottom section */
.bottom-section {
  border-top: 1px solid #3c3c3c;
  background-color: #252526;
  margin-top: auto;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #3c3c3c;
  padding: 0 16px;
}

.tab {
  padding: 12px 16px;
  background: none;
  border: none;
  color: #8c8c8c;
  cursor: pointer;
  font-size: 13px;
  border-bottom: 2px solid transparent;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab.active {
  color: #cccccc;
}

.tab:hover {
  background-color: #2a2d2e;
}

.tab .beta-badge {
  background-color: #6f42c1;
  color: #ffffff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  text-transform: uppercase;
  font-weight: 600;
}

/* Input area */
.input-area {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #1e1e1e;
}

.input-row {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.input-container {
  flex: 1;
  position: relative;
}

.message-input {
  width: 100%;
  min-height: 44px;
  max-height: 120px;
  resize: none;
  padding: 12px 16px;
  border-radius: 6px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  background-color: #3c3c3c;
  border: 1px solid #464647;
  color: #cccccc;
  outline: none;
  transition: border-color 0.2s;
}

.message-input:focus {
  border-color: #007acc;
}

.message-input::placeholder {
  color: #8c8c8c;
  opacity: 1;
}

.agent-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-mode-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #cccccc;
}

.agent-mode-icon {
  color: #007acc;
  font-size: 16px;
}

.agent-mode-dropdown {
  background-color: #3c3c3c;
  color: #007acc;
  border: 1px solid #464647;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  min-width: 80px;
  cursor: pointer;
  font-weight: 500;
}

.agent-mode-dropdown:hover {
  background-color: #404040;
}

.send-btn {
  background-color: transparent;
  color: #cccccc;
  border: none;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 44px;
  transition: background-color 0.2s;
}

.send-btn:hover {
  background-color: #2a2d2e;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.attach-btn {
  background-color: transparent;
  color: #8c8c8c;
  border: none;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 44px;
  transition: all 0.2s;
}

.attach-btn:hover {
  background-color: #2a2d2e;
  color: #cccccc;
}

/* Model selector */
.model-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--vscode-sideBar-background);
  border-bottom: 1px solid var(--vscode-panel-border);
  font-size: 12px;
}

.model-dropdown {
  background-color: var(--vscode-dropdown-background);
  color: var(--vscode-dropdown-foreground);
  border: 1px solid var(--vscode-dropdown-border);
  padding: 4px 8px;
  border-radius: 2px;
  font-size: 12px;
  min-width: 150px;
}

/* Loading states */
.loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--vscode-sideBar-foreground);
  opacity: 0.8;
  font-size: 12px;
}

.loading-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid var(--vscode-sideBar-foreground);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error states */
.error {
  color: #f48771;
  background-color: rgba(244, 135, 113, 0.1);
  border: 1px solid rgba(244, 135, 113, 0.3);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
}

/* Success states */
.success {
  color: #89d185;
  background-color: rgba(137, 209, 133, 0.1);
  border: 1px solid rgba(137, 209, 133, 0.3);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
}
