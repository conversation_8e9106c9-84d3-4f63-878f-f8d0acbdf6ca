import * as vscode from 'vscode';

export interface SIAConfiguration {
    defaultModel: string;
    defaultProvider: string;
    maxTokens: number;
    temperature: number;
    enableLogging: boolean;
    enableTelemetry: boolean;
    autoSave: boolean;
    contextWindow: number;
    maxFileSize: number;
    excludedFileTypes: string[];
    enableCodeCompletion: boolean;
    enableRealTimeAnalysis: boolean;
    theme: 'dark' | 'light' | 'auto';
    panelPosition: 'side' | 'bottom' | 'right';
}

export class ConfigurationService {
    private static instance: ConfigurationService;
    private configuration: vscode.WorkspaceConfiguration;

    private constructor() {
        this.configuration = vscode.workspace.getConfiguration('sia');
        
        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration((event) => {
            if (event.affectsConfiguration('sia')) {
                this.configuration = vscode.workspace.getConfiguration('sia');
                this.notifyConfigurationChange();
            }
        });
    }

    static getInstance(): ConfigurationService {
        if (!ConfigurationService.instance) {
            ConfigurationService.instance = new ConfigurationService();
        }
        return ConfigurationService.instance;
    }

    /**
     * Get the complete SIA configuration
     */
    getConfiguration(): SIAConfiguration {
        return {
            defaultModel: this.configuration.get('defaultModel', 'gemini-2.0-flash-exp'),
            defaultProvider: this.configuration.get('defaultProvider', 'gemini'),
            maxTokens: this.configuration.get('maxTokens', 4096),
            temperature: this.configuration.get('temperature', 0.7),
            enableLogging: this.configuration.get('enableLogging', true),
            enableTelemetry: this.configuration.get('enableTelemetry', false),
            autoSave: this.configuration.get('autoSave', true),
            contextWindow: this.configuration.get('contextWindow', 8192),
            maxFileSize: this.configuration.get('maxFileSize', 1048576), // 1MB
            excludedFileTypes: this.configuration.get('excludedFileTypes', [
                '.exe', '.dll', '.so', '.dylib', '.bin', '.zip', '.tar', '.gz',
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg',
                '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv',
                '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'
            ]),
            enableCodeCompletion: this.configuration.get('enableCodeCompletion', true),
            enableRealTimeAnalysis: this.configuration.get('enableRealTimeAnalysis', false),
            theme: this.configuration.get('theme', 'auto'),
            panelPosition: this.configuration.get('panelPosition', 'side')
        };
    }

    /**
     * Update a configuration value
     */
    async updateConfiguration<K extends keyof SIAConfiguration>(
        key: K,
        value: SIAConfiguration[K],
        target: vscode.ConfigurationTarget = vscode.ConfigurationTarget.Workspace
    ): Promise<void> {
        await this.configuration.update(key, value, target);
    }

    /**
     * Get a specific configuration value
     */
    get<K extends keyof SIAConfiguration>(key: K): SIAConfiguration[K] {
        const config = this.getConfiguration();
        return config[key];
    }

    /**
     * Check if a file type should be excluded
     */
    isFileTypeExcluded(filePath: string): boolean {
        const excludedTypes = this.get('excludedFileTypes');
        const extension = filePath.toLowerCase().substring(filePath.lastIndexOf('.'));
        return excludedTypes.includes(extension);
    }

    /**
     * Check if a file is too large to process
     */
    isFileTooLarge(fileSize: number): boolean {
        const maxSize = this.get('maxFileSize');
        return fileSize > maxSize;
    }

    /**
     * Get the effective theme based on configuration and VS Code theme
     */
    getEffectiveTheme(): 'dark' | 'light' {
        const configTheme = this.get('theme');
        
        if (configTheme === 'auto') {
            // Detect VS Code theme
            const currentTheme = vscode.window.activeColorTheme;
            return currentTheme.kind === vscode.ColorThemeKind.Light ? 'light' : 'dark';
        }
        
        return configTheme;
    }

    /**
     * Get model-specific configuration
     */
    getModelConfiguration(provider: string, model: string) {
        const baseConfig = this.getConfiguration();
        
        // Model-specific overrides
        const modelOverrides = this.configuration.get(`models.${provider}.${model}`, {});
        
        return {
            ...baseConfig,
            ...modelOverrides
        };
    }

    /**
     * Validate configuration values
     */
    validateConfiguration(): { isValid: boolean; errors: string[] } {
        const config = this.getConfiguration();
        const errors: string[] = [];

        // Validate temperature
        if (config.temperature < 0 || config.temperature > 2) {
            errors.push('Temperature must be between 0 and 2');
        }

        // Validate maxTokens
        if (config.maxTokens < 1 || config.maxTokens > 32768) {
            errors.push('Max tokens must be between 1 and 32768');
        }

        // Validate contextWindow
        if (config.contextWindow < 1024 || config.contextWindow > 131072) {
            errors.push('Context window must be between 1024 and 131072');
        }

        // Validate maxFileSize
        if (config.maxFileSize < 1024 || config.maxFileSize > 10485760) { // 10MB max
            errors.push('Max file size must be between 1KB and 10MB');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Reset configuration to defaults
     */
    async resetToDefaults(): Promise<void> {
        const defaultConfig = new ConfigurationService().getConfiguration();
        
        for (const [key, value] of Object.entries(defaultConfig)) {
            await this.updateConfiguration(key as keyof SIAConfiguration, value);
        }
    }

    /**
     * Export configuration for backup
     */
    exportConfiguration(): string {
        const config = this.getConfiguration();
        return JSON.stringify(config, null, 2);
    }

    /**
     * Import configuration from backup
     */
    async importConfiguration(configJson: string): Promise<void> {
        try {
            const config = JSON.parse(configJson) as Partial<SIAConfiguration>;
            
            for (const [key, value] of Object.entries(config)) {
                if (value !== undefined) {
                    await this.updateConfiguration(key as keyof SIAConfiguration, value);
                }
            }
        } catch (error) {
            throw new Error('Invalid configuration format');
        }
    }

    /**
     * Get configuration schema for UI generation
     */
    getConfigurationSchema() {
        return {
            defaultModel: {
                type: 'string',
                description: 'Default AI model to use',
                enum: ['gemini-2.0-flash-exp', 'gemini-1.5-flash', 'gpt-4o', 'claude-3-5-sonnet-20241022']
            },
            defaultProvider: {
                type: 'string',
                description: 'Default AI provider',
                enum: ['gemini', 'openai', 'anthropic', 'mistral', 'deepseek']
            },
            maxTokens: {
                type: 'number',
                description: 'Maximum tokens per request',
                minimum: 1,
                maximum: 32768
            },
            temperature: {
                type: 'number',
                description: 'Model temperature (creativity)',
                minimum: 0,
                maximum: 2,
                step: 0.1
            },
            enableLogging: {
                type: 'boolean',
                description: 'Enable detailed logging'
            },
            enableTelemetry: {
                type: 'boolean',
                description: 'Enable anonymous usage telemetry'
            },
            autoSave: {
                type: 'boolean',
                description: 'Automatically save changes'
            },
            contextWindow: {
                type: 'number',
                description: 'Context window size',
                minimum: 1024,
                maximum: 131072
            },
            maxFileSize: {
                type: 'number',
                description: 'Maximum file size to process (bytes)',
                minimum: 1024,
                maximum: 10485760
            },
            excludedFileTypes: {
                type: 'array',
                description: 'File types to exclude from processing',
                items: { type: 'string' }
            },
            enableCodeCompletion: {
                type: 'boolean',
                description: 'Enable AI-powered code completion'
            },
            enableRealTimeAnalysis: {
                type: 'boolean',
                description: 'Enable real-time code analysis'
            },
            theme: {
                type: 'string',
                description: 'UI theme preference',
                enum: ['dark', 'light', 'auto']
            },
            panelPosition: {
                type: 'string',
                description: 'Panel position in VS Code',
                enum: ['side', 'bottom', 'right']
            }
        };
    }

    /**
     * Notify listeners of configuration changes
     */
    private notifyConfigurationChange(): void {
        // Emit configuration change event
        vscode.commands.executeCommand('sia.configurationChanged', this.getConfiguration());
    }
}
