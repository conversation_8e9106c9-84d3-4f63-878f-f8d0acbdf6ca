import { useEffect, useRef } from 'react';

// VS Code API type definition
interface VscodeApi {
    postMessage(message: any): void;
    getState(): any;
    setState(state: any): void;
}

declare global {
    interface Window {
        acquireVsCodeApi(): VscodeApi;
    }
}

export const useVscodeApi = () => {
    const vscodeRef = useRef<VscodeApi | null>(null);

    useEffect(() => {
        if (!vscodeRef.current) {
            try {
                vscodeRef.current = window.acquireVsCodeApi();
            } catch (error) {
                console.error('Failed to acquire VS Code API:', error);
                // Fallback for development/testing
                vscodeRef.current = {
                    postMessage: (message: any) => {
                        console.log('Mock postMessage:', message);
                    },
                    getState: () => ({}),
                    setState: (state: any) => {
                        console.log('Mock setState:', state);
                    }
                };
            }
        }
    }, []);

    return vscodeRef.current!;
};
