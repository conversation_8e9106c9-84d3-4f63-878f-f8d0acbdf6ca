import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';
import * as ts from 'typescript';
import fetch from 'node-fetch';
import { ToolDefinition, ToolResult } from '../types/tools';
import { WebviewPanelProvider } from '../providers/WebviewPanelProvider';
import { SidePanelProvider } from '../providers/SidePanelProvider';
import { ErrorHandler } from '../services/ErrorHandler';

export class ToolController {
    private context: vscode.ExtensionContext;
    private webviewProvider?: WebviewPanelProvider | SidePanelProvider;
    private tools: Map<string, Function> = new Map();
    private toolDefinitions: ToolDefinition[] = [];
    private errorHandler: ErrorHandler;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.errorHandler = ErrorHandler.getInstance();
        this.registerAllTools();
    }

    setWebviewProvider(provider: WebviewPanelProvider | SidePanelProvider) {
        this.webviewProvider = provider;
    }

    /**
     * Register all available tools
     */
    private registerAllTools() {
        // File System Tools
        this.registerTool('readFile', this.readFile.bind(this), {
            name: 'readFile',
            description: 'Read the content of a file',
            parameters: {
                type: 'object',
                properties: {
                    path: { type: 'string', description: 'Path to the file to read' }
                },
                required: ['path']
            }
        });

        this.registerTool('writeFile', this.writeFile.bind(this), {
            name: 'writeFile',
            description: 'Write content to a file',
            parameters: {
                type: 'object',
                properties: {
                    path: { type: 'string', description: 'Path to the file to write' },
                    content: { type: 'string', description: 'Content to write to the file' }
                },
                required: ['path', 'content']
            }
        });

        this.registerTool('createFile', this.createFile.bind(this), {
            name: 'createFile',
            description: 'Create a new empty file',
            parameters: {
                type: 'object',
                properties: {
                    path: { type: 'string', description: 'Path where to create the file' }
                },
                required: ['path']
            }
        });

        this.registerTool('readDirectory', this.readDirectory.bind(this), {
            name: 'readDirectory',
            description: 'List files and directories in a directory',
            parameters: {
                type: 'object',
                properties: {
                    path: { type: 'string', description: 'Path to the directory to read' }
                },
                required: ['path']
            }
        });

        this.registerTool('createDirectory', this.createDirectory.bind(this), {
            name: 'createDirectory',
            description: 'Create a new directory',
            parameters: {
                type: 'object',
                properties: {
                    path: { type: 'string', description: 'Path where to create the directory' }
                },
                required: ['path']
            }
        });

        this.registerTool('moveFile', this.moveFile.bind(this), {
            name: 'moveFile',
            description: 'Move or rename a file or directory',
            parameters: {
                type: 'object',
                properties: {
                    source: { type: 'string', description: 'Source path' },
                    destination: { type: 'string', description: 'Destination path' }
                },
                required: ['source', 'destination']
            }
        });

        this.registerTool('deleteFile', this.deleteFile.bind(this), {
            name: 'deleteFile',
            description: 'Delete a file or directory',
            parameters: {
                type: 'object',
                properties: {
                    path: { type: 'string', description: 'Path to delete' }
                },
                required: ['path']
            }
        });

        this.registerTool('fileExists', this.fileExists.bind(this), {
            name: 'fileExists',
            description: 'Check if a file or directory exists',
            parameters: {
                type: 'object',
                properties: {
                    path: { type: 'string', description: 'Path to check' }
                },
                required: ['path']
            }
        });

        this.registerTool('searchInFile', this.searchInFile.bind(this), {
            name: 'searchInFile',
            description: 'Search for text or regex pattern in a file',
            parameters: {
                type: 'object',
                properties: {
                    path: { type: 'string', description: 'Path to the file to search in' },
                    pattern: { type: 'string', description: 'Text or regex pattern to search for' },
                    isRegex: { type: 'boolean', description: 'Whether the pattern is a regex' }
                },
                required: ['path', 'pattern']
            }
        });

        this.registerTool('replaceInFile', this.replaceInFile.bind(this), {
            name: 'replaceInFile',
            description: 'Replace text in a file',
            parameters: {
                type: 'object',
                properties: {
                    path: { type: 'string', description: 'Path to the file' },
                    searchText: { type: 'string', description: 'Text to search for' },
                    replaceText: { type: 'string', description: 'Text to replace with' },
                    isRegex: { type: 'boolean', description: 'Whether searchText is a regex' }
                },
                required: ['path', 'searchText', 'replaceText']
            }
        });

        // Code Intelligence Tools
        this.registerTool('getCodebaseAST', this.getCodebaseAST.bind(this), {
            name: 'getCodebaseAST',
            description: 'Parse files into Abstract Syntax Trees for analysis',
            parameters: {
                type: 'object',
                properties: {
                    globPattern: { type: 'string', description: 'Glob pattern to match files (e.g., src/**/*.ts)' }
                },
                required: ['globPattern']
            }
        });

        this.registerTool('findSymbolDefinition', this.findSymbolDefinition.bind(this), {
            name: 'findSymbolDefinition',
            description: 'Find the definition of a symbol (function, class, variable)',
            parameters: {
                type: 'object',
                properties: {
                    symbolName: { type: 'string', description: 'Name of the symbol to find' }
                },
                required: ['symbolName']
            }
        });

        this.registerTool('getProjectInfo', this.getProjectInfo.bind(this), {
            name: 'getProjectInfo',
            description: 'Get information about the current project structure and configuration',
            parameters: {
                type: 'object',
                properties: {},
                required: []
            }
        });

        // Terminal and Execution Tools
        this.registerTool('runInTerminal', this.runInTerminal.bind(this), {
            name: 'runInTerminal',
            description: 'Execute a command in the terminal',
            parameters: {
                type: 'object',
                properties: {
                    command: { type: 'string', description: 'Command to execute' },
                    description: { type: 'string', description: 'Description of what the command does' }
                },
                required: ['command']
            }
        });

        this.registerTool('runTests', this.runTests.bind(this), {
            name: 'runTests',
            description: 'Run the project test suite',
            parameters: {
                type: 'object',
                properties: {
                    testCommand: { type: 'string', description: 'Custom test command (optional)' }
                },
                required: []
            }
        });

        // Web and Knowledge Tools
        this.registerTool('webSearch', this.webSearch.bind(this), {
            name: 'webSearch',
            description: 'Search the web for information',
            parameters: {
                type: 'object',
                properties: {
                    query: { type: 'string', description: 'Search query' }
                },
                required: ['query']
            }
        });

        this.registerTool('fetchWebpage', this.fetchWebpage.bind(this), {
            name: 'fetchWebpage',
            description: 'Fetch content from a webpage URL',
            parameters: {
                type: 'object',
                properties: {
                    url: { type: 'string', description: 'URL to fetch' }
                },
                required: ['url']
            }
        });

        this.registerTool('githubSearch', this.githubSearch.bind(this), {
            name: 'githubSearch',
            description: 'Search GitHub repositories and code',
            parameters: {
                type: 'object',
                properties: {
                    query: { type: 'string', description: 'GitHub search query' },
                    type: { type: 'string', enum: ['repositories', 'code', 'issues'], description: 'Type of search' }
                },
                required: ['query']
            }
        });

        this.registerTool('packageSearch', this.packageSearch.bind(this), {
            name: 'packageSearch',
            description: 'Search for packages in npm, PyPI, or other package managers',
            parameters: {
                type: 'object',
                properties: {
                    packageName: { type: 'string', description: 'Package name to search for' },
                    ecosystem: { type: 'string', enum: ['npm', 'pypi', 'cargo', 'maven'], description: 'Package ecosystem' }
                },
                required: ['packageName']
            }
        });

        this.registerTool('installPackage', this.installPackage.bind(this), {
            name: 'installPackage',
            description: 'Install a package using the appropriate package manager',
            parameters: {
                type: 'object',
                properties: {
                    packageName: { type: 'string', description: 'Package name to install' },
                    isDev: { type: 'boolean', description: 'Install as dev dependency' },
                    version: { type: 'string', description: 'Specific version to install' }
                },
                required: ['packageName']
            }
        });

        this.registerTool('lintCode', this.lintCode.bind(this), {
            name: 'lintCode',
            description: 'Run linting on code files',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to file or directory to lint' },
                    fix: { type: 'boolean', description: 'Automatically fix linting issues' }
                },
                required: ['filePath']
            }
        });

        this.registerTool('formatCode', this.formatCode.bind(this), {
            name: 'formatCode',
            description: 'Format code using appropriate formatter',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to file to format' }
                },
                required: ['filePath']
            }
        });

        // User Interaction Tools
        this.registerTool('showDiffForApproval', this.showDiffForApproval.bind(this), {
            name: 'showDiffForApproval',
            description: 'Show a diff for user approval before making changes',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file being modified' },
                    newContent: { type: 'string', description: 'New content for the file' }
                },
                required: ['filePath', 'newContent']
            }
        });

        this.registerTool('askUserForInput', this.askUserForInput.bind(this), {
            name: 'askUserForInput',
            description: 'Ask the user for input or clarification',
            parameters: {
                type: 'object',
                properties: {
                    question: { type: 'string', description: 'Question to ask the user' }
                },
                required: ['question']
            }
        });

        // Advanced Code Tools
        this.registerTool('generateTests', this.generateTests.bind(this), {
            name: 'generateTests',
            description: 'Generate unit tests for a given file or function',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to generate tests for' },
                    functionName: { type: 'string', description: 'Specific function to test (optional)' }
                },
                required: ['filePath']
            }
        });

        this.registerTool('refactorCode', this.refactorCode.bind(this), {
            name: 'refactorCode',
            description: 'Refactor code according to given instructions',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to refactor' },
                    instructions: { type: 'string', description: 'Refactoring instructions' }
                },
                required: ['filePath', 'instructions']
            }
        });

        this.registerTool('optimizeCode', this.optimizeCode.bind(this), {
            name: 'optimizeCode',
            description: 'Optimize code for performance and readability',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to optimize' }
                },
                required: ['filePath']
            }
        });

        this.registerTool('addDocumentation', this.addDocumentation.bind(this), {
            name: 'addDocumentation',
            description: 'Add comprehensive documentation to code',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to document' }
                },
                required: ['filePath']
            }
        });

        this.registerTool('analyzeCodeQuality', this.analyzeCodeQuality.bind(this), {
            name: 'analyzeCodeQuality',
            description: 'Analyze code quality and suggest improvements',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to analyze' }
                },
                required: ['filePath']
            }
        });
    }

    /**
     * Register a tool with its implementation and definition
     */
    private registerTool(name: string, implementation: Function, definition: ToolDefinition) {
        this.tools.set(name, implementation);
        this.toolDefinitions.push(definition);
    }

    /**
     * Execute a tool by name
     */
    async executeTool(toolName: string, parameters: any): Promise<ToolResult> {
        const tool = this.tools.get(toolName);
        if (!tool) {
            return {
                success: false,
                error: `Tool '${toolName}' not found`
            };
        }

        try {
            const result = await tool(parameters);
            return result;
        } catch (error: any) {
            const errorInfo = this.errorHandler.handleToolError(toolName, error, parameters);
            console.error(`Error executing tool '${toolName}':`, error);
            return {
                success: false,
                error: errorInfo.message
            };
        }
    }

    /**
     * Get all tool definitions for LLM function calling
     */
    getToolDefinitions(): ToolDefinition[] {
        return this.toolDefinitions;
    }

    // Tool Implementations

    private async readFile(params: { path: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const fullPath = path.isAbsolute(params.path) ? params.path : path.join(workspaceRoot, params.path);
            const uri = vscode.Uri.file(fullPath);
            const content = await vscode.workspace.fs.readFile(uri);

            return {
                success: true,
                data: Buffer.from(content).toString('utf8')
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to read file: ${error.message}`
            };
        }
    }

    private async writeFile(params: { path: string; content: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const fullPath = path.isAbsolute(params.path) ? params.path : path.join(workspaceRoot, params.path);
            const uri = vscode.Uri.file(fullPath);

            // Create directory if it doesn't exist
            const dirPath = path.dirname(fullPath);
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
            }

            const content = Buffer.from(params.content, 'utf8');
            await vscode.workspace.fs.writeFile(uri, content);

            return {
                success: true,
                data: `File written successfully: ${params.path}`
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to write file: ${error.message}`
            };
        }
    }

    private async createFile(params: { path: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const fullPath = path.isAbsolute(params.path) ? params.path : path.join(workspaceRoot, params.path);
            const uri = vscode.Uri.file(fullPath);

            // Check if file already exists
            try {
                await vscode.workspace.fs.stat(uri);
                return { success: false, error: 'File already exists' };
            } catch {
                // File doesn't exist, which is what we want
            }

            // Create directory if it doesn't exist
            const dirPath = path.dirname(fullPath);
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
            }

            await vscode.workspace.fs.writeFile(uri, new Uint8Array());

            return {
                success: true,
                data: `File created successfully: ${params.path}`
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to create file: ${error.message}`
            };
        }
    }

    private async readDirectory(params: { path: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const fullPath = path.isAbsolute(params.path) ? params.path : path.join(workspaceRoot, params.path);
            const uri = vscode.Uri.file(fullPath);
            const entries = await vscode.workspace.fs.readDirectory(uri);

            const result = entries.map(([name, type]) => ({
                name,
                type: type === vscode.FileType.Directory ? 'directory' : 'file'
            }));

            return {
                success: true,
                data: result
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to read directory: ${error.message}`
            };
        }
    }

    private async createDirectory(params: { path: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const fullPath = path.isAbsolute(params.path) ? params.path : path.join(workspaceRoot, params.path);
            const uri = vscode.Uri.file(fullPath);

            await vscode.workspace.fs.createDirectory(uri);

            return {
                success: true,
                data: `Directory created successfully: ${params.path}`
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to create directory: ${error.message}`
            };
        }
    }

    private async moveFile(params: { source: string; destination: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const sourcePath = path.isAbsolute(params.source) ? params.source : path.join(workspaceRoot, params.source);
            const destPath = path.isAbsolute(params.destination) ? params.destination : path.join(workspaceRoot, params.destination);

            const sourceUri = vscode.Uri.file(sourcePath);
            const destUri = vscode.Uri.file(destPath);

            await vscode.workspace.fs.rename(sourceUri, destUri);

            return {
                success: true,
                data: `Moved ${params.source} to ${params.destination}`
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to move file: ${error.message}`
            };
        }
    }

    private async deleteFile(params: { path: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const fullPath = path.isAbsolute(params.path) ? params.path : path.join(workspaceRoot, params.path);
            const uri = vscode.Uri.file(fullPath);

            await vscode.workspace.fs.delete(uri, { recursive: true });

            return {
                success: true,
                data: `Deleted: ${params.path}`
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to delete: ${error.message}`
            };
        }
    }

    private async fileExists(params: { path: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const fullPath = path.isAbsolute(params.path) ? params.path : path.join(workspaceRoot, params.path);
            const uri = vscode.Uri.file(fullPath);

            try {
                await vscode.workspace.fs.stat(uri);
                return { success: true, data: true };
            } catch {
                return { success: true, data: false };
            }
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to check file existence: ${error.message}`
            };
        }
    }

    private async searchInFile(params: { path: string; pattern: string; isRegex?: boolean }): Promise<ToolResult> {
        try {
            const fileResult = await this.readFile({ path: params.path });
            if (!fileResult.success) {
                return fileResult;
            }

            const content = fileResult.data as string;
            const lines = content.split('\n');
            const matches: { line: number; content: string; match: string }[] = [];

            if (params.isRegex) {
                const regex = new RegExp(params.pattern, 'gi');
                lines.forEach((line, index) => {
                    const match = line.match(regex);
                    if (match) {
                        matches.push({
                            line: index + 1,
                            content: line,
                            match: match[0]
                        });
                    }
                });
            } else {
                lines.forEach((line, index) => {
                    if (line.toLowerCase().includes(params.pattern.toLowerCase())) {
                        matches.push({
                            line: index + 1,
                            content: line,
                            match: params.pattern
                        });
                    }
                });
            }

            return {
                success: true,
                data: {
                    matches,
                    totalMatches: matches.length
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to search in file: ${error.message}`
            };
        }
    }

    private async replaceInFile(params: { path: string; searchText: string; replaceText: string; isRegex?: boolean }): Promise<ToolResult> {
        try {
            const fileResult = await this.readFile({ path: params.path });
            if (!fileResult.success) {
                return fileResult;
            }

            let content = fileResult.data as string;
            let replacements = 0;

            if (params.isRegex) {
                const regex = new RegExp(params.searchText, 'g');
                const matches = content.match(regex);
                replacements = matches ? matches.length : 0;
                content = content.replace(regex, params.replaceText);
            } else {
                const originalLength = content.length;
                content = content.split(params.searchText).join(params.replaceText);
                replacements = (originalLength - content.length) / (params.searchText.length - params.replaceText.length);
            }

            const writeResult = await this.writeFile({ path: params.path, content });
            if (!writeResult.success) {
                return writeResult;
            }

            return {
                success: true,
                data: `Made ${replacements} replacements in ${params.path}`
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to replace in file: ${error.message}`
            };
        }
    }

    private async getCodebaseAST(params: { globPattern: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const files = await new Promise<string[]>((resolve, reject) => {
                glob(params.globPattern, { cwd: workspaceRoot }, (err, matches) => {
                    if (err) reject(err);
                    else resolve(matches);
                });
            });
            const results: { filePath: string; ast: any; exports: string[]; imports: string[] }[] = [];

            for (const file of files.slice(0, 10)) { // Limit to 10 files to avoid overwhelming
                try {
                    const fullPath = path.join(workspaceRoot, file);
                    const content = fs.readFileSync(fullPath, 'utf8');

                    if (file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js') || file.endsWith('.jsx')) {
                        const sourceFile = ts.createSourceFile(
                            file,
                            content,
                            ts.ScriptTarget.Latest,
                            true
                        );

                        const exports: string[] = [];
                        const imports: string[] = [];

                        const visit = (node: ts.Node) => {
                            if (ts.isExportDeclaration(node) || ts.isExportAssignment(node)) {
                                exports.push(node.getText());
                            }
                            if (ts.isImportDeclaration(node)) {
                                imports.push(node.getText());
                            }
                            ts.forEachChild(node, visit);
                        };

                        visit(sourceFile);

                        results.push({
                            filePath: file,
                            ast: 'AST parsed successfully',
                            exports,
                            imports
                        });
                    }
                } catch (error) {
                    console.error(`Error parsing ${file}:`, error);
                }
            }

            return {
                success: true,
                data: {
                    files: results,
                    totalFiles: files.length,
                    parsedFiles: results.length
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to get codebase AST: ${error.message}`
            };
        }
    }

    private async findSymbolDefinition(params: { symbolName: string }): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            // Search for symbol in TypeScript/JavaScript files
            const files = await new Promise<string[]>((resolve, reject) => {
                glob('**/*.{ts,tsx,js,jsx}', { cwd: workspaceRoot }, (err, matches) => {
                    if (err) reject(err);
                    else resolve(matches);
                });
            });
            const definitions: { file: string; line: number; content: string }[] = [];

            for (const file of files) {
                try {
                    const fullPath = path.join(workspaceRoot, file);
                    const content = fs.readFileSync(fullPath, 'utf8');
                    const lines = content.split('\n');

                    lines.forEach((line, index) => {
                        // Look for function, class, interface, type, const, let, var declarations
                        const patterns = [
                            new RegExp(`\\b(function|class|interface|type|const|let|var)\\s+${params.symbolName}\\b`),
                            new RegExp(`\\b${params.symbolName}\\s*[:=]`),
                            new RegExp(`\\bexport\\s+.*\\b${params.symbolName}\\b`)
                        ];

                        for (const pattern of patterns) {
                            if (pattern.test(line)) {
                                definitions.push({
                                    file,
                                    line: index + 1,
                                    content: line.trim()
                                });
                                break;
                            }
                        }
                    });
                } catch (error) {
                    console.error(`Error searching in ${file}:`, error);
                }
            }

            return {
                success: true,
                data: {
                    symbol: params.symbolName,
                    definitions,
                    totalFound: definitions.length
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to find symbol definition: ${error.message}`
            };
        }
    }

    private async getProjectInfo(): Promise<ToolResult> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return { success: false, error: 'No workspace folder open' };
            }

            const projectInfo: any = {
                root: workspaceRoot,
                name: path.basename(workspaceRoot),
                type: 'unknown',
                packageManager: null,
                framework: null,
                dependencies: [],
                devDependencies: [],
                scripts: {},
                testFramework: null
            };

            // Check for package.json (Node.js project)
            const packageJsonPath = path.join(workspaceRoot, 'package.json');
            if (fs.existsSync(packageJsonPath)) {
                try {
                    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
                    projectInfo.type = 'node';
                    projectInfo.name = packageJson.name || projectInfo.name;
                    projectInfo.dependencies = Object.keys(packageJson.dependencies || {});
                    projectInfo.devDependencies = Object.keys(packageJson.devDependencies || {});
                    projectInfo.scripts = packageJson.scripts || {};

                    // Detect package manager
                    if (fs.existsSync(path.join(workspaceRoot, 'yarn.lock'))) {
                        projectInfo.packageManager = 'yarn';
                    } else if (fs.existsSync(path.join(workspaceRoot, 'pnpm-lock.yaml'))) {
                        projectInfo.packageManager = 'pnpm';
                    } else if (fs.existsSync(path.join(workspaceRoot, 'package-lock.json'))) {
                        projectInfo.packageManager = 'npm';
                    }

                    // Detect framework
                    const deps = [...projectInfo.dependencies, ...projectInfo.devDependencies];
                    if (deps.includes('react')) projectInfo.framework = 'React';
                    else if (deps.includes('vue')) projectInfo.framework = 'Vue';
                    else if (deps.includes('angular')) projectInfo.framework = 'Angular';
                    else if (deps.includes('next')) projectInfo.framework = 'Next.js';
                    else if (deps.includes('nuxt')) projectInfo.framework = 'Nuxt.js';
                    else if (deps.includes('express')) projectInfo.framework = 'Express';

                    // Detect test framework
                    if (deps.includes('jest')) projectInfo.testFramework = 'Jest';
                    else if (deps.includes('mocha')) projectInfo.testFramework = 'Mocha';
                    else if (deps.includes('vitest')) projectInfo.testFramework = 'Vitest';
                    else if (deps.includes('cypress')) projectInfo.testFramework = 'Cypress';
                } catch (error) {
                    console.error('Error parsing package.json:', error);
                }
            }

            // Check for Python project
            if (fs.existsSync(path.join(workspaceRoot, 'requirements.txt')) ||
                fs.existsSync(path.join(workspaceRoot, 'pyproject.toml')) ||
                fs.existsSync(path.join(workspaceRoot, 'setup.py'))) {
                projectInfo.type = 'python';
                projectInfo.packageManager = 'pip';
            }

            // Check for Rust project
            if (fs.existsSync(path.join(workspaceRoot, 'Cargo.toml'))) {
                projectInfo.type = 'rust';
                projectInfo.packageManager = 'cargo';
            }

            // Check for Go project
            if (fs.existsSync(path.join(workspaceRoot, 'go.mod'))) {
                projectInfo.type = 'go';
                projectInfo.packageManager = 'go';
            }

            // Check for Java project
            if (fs.existsSync(path.join(workspaceRoot, 'pom.xml'))) {
                projectInfo.type = 'java';
                projectInfo.packageManager = 'maven';
            } else if (fs.existsSync(path.join(workspaceRoot, 'build.gradle'))) {
                projectInfo.type = 'java';
                projectInfo.packageManager = 'gradle';
            }

            return {
                success: true,
                data: projectInfo
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to get project info: ${error.message}`
            };
        }
    }

    private async runInTerminal(params: { command: string; description?: string }): Promise<ToolResult> {
        try {
            const terminal = vscode.window.createTerminal({
                name: 'SIA Assistant',
                cwd: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
            });

            terminal.show();
            terminal.sendText(params.command);

            return {
                success: true,
                data: `Executed command: ${params.command}${params.description ? ` (${params.description})` : ''}`
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to run command in terminal: ${error.message}`
            };
        }
    }

    private async runTests(params: { testCommand?: string }): Promise<ToolResult> {
        try {
            const projectInfo = await this.getProjectInfo();
            if (!projectInfo.success) {
                return projectInfo;
            }

            const info = projectInfo.data;
            let testCommand = params.testCommand;

            if (!testCommand) {
                // Auto-detect test command
                if (info.scripts && info.scripts.test) {
                    testCommand = `${info.packageManager || 'npm'} test`;
                } else if (info.testFramework === 'Jest') {
                    testCommand = 'npx jest';
                } else if (info.testFramework === 'Mocha') {
                    testCommand = 'npx mocha';
                } else if (info.testFramework === 'Vitest') {
                    testCommand = 'npx vitest run';
                } else if (info.type === 'python') {
                    testCommand = 'python -m pytest';
                } else if (info.type === 'rust') {
                    testCommand = 'cargo test';
                } else if (info.type === 'go') {
                    testCommand = 'go test ./...';
                } else {
                    return {
                        success: false,
                        error: 'No test command found. Please specify a test command.'
                    };
                }
            }

            return await this.runInTerminal({
                command: testCommand,
                description: 'Running tests'
            });
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to run tests: ${error.message}`
            };
        }
    }

    private async webSearch(params: { query: string }): Promise<ToolResult> {
        try {
            // Implement a basic web search using DuckDuckGo Instant Answer API
            const searchUrl = `https://api.duckduckgo.com/?q=${encodeURIComponent(params.query)}&format=json&no_html=1&skip_disambig=1`;

            const response = await fetch(searchUrl);
            const data = await response.json() as any;

            const results = {
                query: params.query,
                abstract: data.Abstract || 'No abstract available',
                abstractSource: data.AbstractSource || '',
                abstractUrl: data.AbstractURL || '',
                relatedTopics: data.RelatedTopics?.slice(0, 5).map((topic: any) => ({
                    text: topic.Text,
                    url: topic.FirstURL
                })) || [],
                answer: data.Answer || '',
                answerType: data.AnswerType || ''
            };

            return {
                success: true,
                data: results
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Web search failed: ${error.message}`
            };
        }
    }

    private async fetchWebpage(params: { url: string }): Promise<ToolResult> {
        try {
            const response = await fetch(params.url);
            const content = await response.text();

            // Basic content extraction (remove HTML tags for simple text)
            const textContent = content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();

            return {
                success: true,
                data: {
                    url: params.url,
                    title: this.extractTitle(content),
                    content: textContent.substring(0, 5000), // Limit content length
                    contentLength: textContent.length,
                    status: response.status
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to fetch webpage: ${error.message}`
            };
        }
    }

    private async githubSearch(params: { query: string; type?: string }): Promise<ToolResult> {
        try {
            const searchType = params.type || 'repositories';
            const apiUrl = `https://api.github.com/search/${searchType}?q=${encodeURIComponent(params.query)}&per_page=10`;

            const response = await fetch(apiUrl, {
                headers: {
                    'Accept': 'application/vnd.github.v3+json',
                    'User-Agent': 'SIA-AI-Assistant'
                }
            });

            const data = await response.json() as any;

            const results = data.items?.map((item: any) => ({
                name: item.name || item.repository?.name,
                fullName: item.full_name || item.repository?.full_name,
                description: item.description || item.repository?.description,
                url: item.html_url || item.repository?.html_url,
                stars: item.stargazers_count || item.repository?.stargazers_count,
                language: item.language || item.repository?.language,
                updatedAt: item.updated_at || item.repository?.updated_at
            })) || [];

            return {
                success: true,
                data: {
                    query: params.query,
                    type: searchType,
                    totalCount: data.total_count || 0,
                    results
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `GitHub search failed: ${error.message}`
            };
        }
    }

    private async packageSearch(params: { packageName: string; ecosystem?: string }): Promise<ToolResult> {
        try {
            const ecosystem = params.ecosystem || 'npm';
            let apiUrl = '';

            switch (ecosystem) {
                case 'npm':
                    apiUrl = `https://registry.npmjs.org/-/v1/search?text=${encodeURIComponent(params.packageName)}&size=10`;
                    break;
                case 'pypi':
                    apiUrl = `https://pypi.org/pypi/${encodeURIComponent(params.packageName)}/json`;
                    break;
                default:
                    return {
                        success: false,
                        error: `Unsupported package ecosystem: ${ecosystem}`
                    };
            }

            const response = await fetch(apiUrl);
            const data = await response.json() as any;

            let results;
            if (ecosystem === 'npm') {
                results = data.objects?.map((obj: any) => ({
                    name: obj.package.name,
                    version: obj.package.version,
                    description: obj.package.description,
                    author: obj.package.author?.name,
                    keywords: obj.package.keywords,
                    npmUrl: `https://www.npmjs.com/package/${obj.package.name}`
                })) || [];
            } else if (ecosystem === 'pypi') {
                results = [{
                    name: data.info?.name,
                    version: data.info?.version,
                    description: data.info?.summary,
                    author: data.info?.author,
                    pypiUrl: `https://pypi.org/project/${data.info?.name}/`
                }];
            }

            return {
                success: true,
                data: {
                    packageName: params.packageName,
                    ecosystem,
                    results
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Package search failed: ${error.message}`
            };
        }
    }

    private async installPackage(params: { packageName: string; isDev?: boolean; version?: string }): Promise<ToolResult> {
        try {
            const projectInfo = await this.getProjectInfo();
            if (!projectInfo.success) {
                return projectInfo;
            }

            const info = projectInfo.data;
            let command = '';

            if (info.type === 'node') {
                const packageManager = info.packageManager || 'npm';
                const versionSpec = params.version ? `@${params.version}` : '';
                const devFlag = params.isDev ? (packageManager === 'npm' ? '--save-dev' : '-D') : '';

                command = `${packageManager} ${packageManager === 'yarn' ? 'add' : 'install'} ${params.packageName}${versionSpec} ${devFlag}`.trim();
            } else if (info.type === 'python') {
                const versionSpec = params.version ? `==${params.version}` : '';
                command = `pip install ${params.packageName}${versionSpec}`;
            } else {
                return {
                    success: false,
                    error: `Package installation not supported for project type: ${info.type}`
                };
            }

            return await this.runInTerminal({ command, description: `Installing package ${params.packageName}` });
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to install package: ${error.message}`
            };
        }
    }

    private async lintCode(params: { filePath: string; fix?: boolean }): Promise<ToolResult> {
        try {
            const projectInfo = await this.getProjectInfo();
            if (!projectInfo.success) {
                return projectInfo;
            }

            const info = projectInfo.data;
            let command = '';

            if (info.type === 'node') {
                // Check for ESLint
                if (info.devDependencies.includes('eslint')) {
                    command = `npx eslint ${params.filePath}${params.fix ? ' --fix' : ''}`;
                } else {
                    return {
                        success: false,
                        error: 'ESLint not found in project dependencies'
                    };
                }
            } else if (info.type === 'python') {
                // Check for common Python linters
                command = `python -m flake8 ${params.filePath}`;
            } else {
                return {
                    success: false,
                    error: `Linting not supported for project type: ${info.type}`
                };
            }

            return await this.runInTerminal({ command, description: `Linting ${params.filePath}` });
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to lint code: ${error.message}`
            };
        }
    }

    private async formatCode(params: { filePath: string }): Promise<ToolResult> {
        try {
            const projectInfo = await this.getProjectInfo();
            if (!projectInfo.success) {
                return projectInfo;
            }

            const info = projectInfo.data;
            let command = '';

            if (info.type === 'node') {
                // Check for Prettier
                if (info.devDependencies.includes('prettier')) {
                    command = `npx prettier --write ${params.filePath}`;
                } else {
                    return {
                        success: false,
                        error: 'Prettier not found in project dependencies'
                    };
                }
            } else if (info.type === 'python') {
                // Use black for Python formatting
                command = `python -m black ${params.filePath}`;
            } else {
                return {
                    success: false,
                    error: `Code formatting not supported for project type: ${info.type}`
                };
            }

            return await this.runInTerminal({ command, description: `Formatting ${params.filePath}` });
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to format code: ${error.message}`
            };
        }
    }

    private async showDiffForApproval(params: { filePath: string; newContent: string }): Promise<ToolResult> {
        try {
            // Read current file content
            const currentResult = await this.readFile({ path: params.filePath });
            const currentContent = currentResult.success ? currentResult.data as string : '';

            // Generate a unique diff ID
            const diffId = `diff_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

            // Send diff to webview for approval
            if (this.webviewProvider) {
                this.webviewProvider.sendMessage({
                    type: 'showDiff',
                    payload: {
                        diffId,
                        original: currentContent,
                        modified: params.newContent,
                        filePath: params.filePath
                    },
                    mode: 'agent'
                });
            }

            return {
                success: true,
                requiresApproval: true,
                diffId,
                data: 'Diff sent for user approval'
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to show diff: ${error.message}`
            };
        }
    }

    private async askUserForInput(params: { question: string }): Promise<ToolResult> {
        try {
            const questionId = `question_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

            // Send question to webview
            if (this.webviewProvider) {
                this.webviewProvider.sendMessage({
                    type: 'askUser',
                    payload: {
                        questionId,
                        question: params.question
                    },
                    mode: 'agent'
                });
            }

            return {
                success: true,
                requiresApproval: true,
                data: {
                    questionId,
                    message: 'Question sent to user, waiting for response'
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to ask user for input: ${error.message}`
            };
        }
    }

    /**
     * Handle user responses to questions and diff approvals
     */
    async handleUserResponse(type: 'diffApproval' | 'userInput', payload: any): Promise<ToolResult> {
        try {
            if (type === 'diffApproval') {
                const { diffId, approved, filePath, newContent } = payload;

                if (approved) {
                    // User approved the diff, write the file
                    const writeResult = await this.writeFile({ path: filePath, content: newContent });
                    return {
                        success: writeResult.success,
                        data: writeResult.success ? 'Changes approved and applied' : writeResult.error,
                        error: writeResult.success ? undefined : writeResult.error
                    };
                } else {
                    return {
                        success: false,
                        data: 'Changes rejected by user'
                    };
                }
            } else if (type === 'userInput') {
                const { questionId, response } = payload;
                return {
                    success: true,
                    data: {
                        questionId,
                        userResponse: response
                    }
                };
            }

            return {
                success: false,
                error: 'Unknown response type'
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to handle user response: ${error.message}`
            };
        }
    }

    // Advanced Tool Implementations

    private async generateTests(params: { filePath: string; functionName?: string }): Promise<ToolResult> {
        try {
            const fileResult = await this.readFile({ path: params.filePath });
            if (!fileResult.success) {
                return fileResult;
            }

            const content = fileResult.data as string;
            const projectInfo = await this.getProjectInfo();

            // Analyze the code and generate appropriate tests
            const testContent = this.generateTestContent(content, params.filePath, params.functionName, projectInfo.data);

            // Determine test file path
            const testFilePath = this.getTestFilePath(params.filePath, projectInfo.data);

            return {
                success: true,
                data: {
                    testFilePath,
                    testContent,
                    message: `Generated tests for ${params.filePath}${params.functionName ? ` (function: ${params.functionName})` : ''}`
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to generate tests: ${error.message}`
            };
        }
    }

    private async refactorCode(params: { filePath: string; instructions: string }): Promise<ToolResult> {
        try {
            const fileResult = await this.readFile({ path: params.filePath });
            if (!fileResult.success) {
                return fileResult;
            }

            const content = fileResult.data as string;

            // Perform actual code refactoring based on instructions
            const refactoredContent = this.performCodeRefactoring(content, params.instructions, params.filePath);
            const suggestions = this.generateRefactoringSuggestions(content, params.filePath);

            return {
                success: true,
                data: {
                    originalContent: content,
                    refactoredContent: refactoredContent,
                    instructions: params.instructions,
                    message: `Code refactoring completed for ${params.filePath}`,
                    suggestions: suggestions
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to refactor code: ${error.message}`
            };
        }
    }

    private async optimizeCode(params: { filePath: string }): Promise<ToolResult> {
        try {
            const fileResult = await this.readFile({ path: params.filePath });
            if (!fileResult.success) {
                return fileResult;
            }

            const content = fileResult.data as string;
            const optimizations = this.analyzeOptimizations(content, params.filePath);

            return {
                success: true,
                data: {
                    filePath: params.filePath,
                    optimizations,
                    message: `Code optimization analysis completed for ${params.filePath}`
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to optimize code: ${error.message}`
            };
        }
    }

    private async addDocumentation(params: { filePath: string }): Promise<ToolResult> {
        try {
            const fileResult = await this.readFile({ path: params.filePath });
            if (!fileResult.success) {
                return fileResult;
            }

            const content = fileResult.data as string;
            const documentedContent = this.addCodeDocumentation(content, params.filePath);

            return {
                success: true,
                data: {
                    originalContent: content,
                    documentedContent,
                    message: `Documentation added to ${params.filePath}`
                }
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to add documentation: ${error.message}`
            };
        }
    }

    private async analyzeCodeQuality(params: { filePath: string }): Promise<ToolResult> {
        try {
            const fileResult = await this.readFile({ path: params.filePath });
            if (!fileResult.success) {
                return fileResult;
            }

            const content = fileResult.data as string;
            const analysis = this.performCodeQualityAnalysis(content, params.filePath);

            return {
                success: true,
                data: analysis
            };
        } catch (error: any) {
            return {
                success: false,
                error: `Failed to analyze code quality: ${error.message}`
            };
        }
    }

    // Helper Methods

    private generateTestContent(content: string, filePath: string, functionName?: string, projectInfo?: any): string {
        const fileExtension = path.extname(filePath);
        const isTypeScript = fileExtension === '.ts' || fileExtension === '.tsx';
        const isJavaScript = fileExtension === '.js' || fileExtension === '.jsx';

        if (isTypeScript || isJavaScript) {
            return this.generateJSTestContent(content, filePath, functionName, projectInfo);
        }

        return `// Generated test file for ${filePath}\n// TODO: Implement tests\n`;
    }

    private generateJSTestContent(content: string, filePath: string, functionName?: string, projectInfo?: any): string {
        const fileName = path.basename(filePath, path.extname(filePath));
        const testFramework = projectInfo?.testFramework || 'Jest';

        // Extract functions and classes from the content
        const functions = this.extractFunctionsFromCode(content);
        const classes = this.extractClassesFromCode(content);

        let testContent = '';

        if (testFramework === 'Jest') {
            // Generate imports
            const exports = [...functions, ...classes];
            if (exports.length > 0) {
                testContent += `import { ${exports.join(', ')} } from './${fileName}';\n\n`;
            } else {
                testContent += `import * as ${fileName} from './${fileName}';\n\n`;
            }

            testContent += `describe('${fileName}', () => {\n`;

            // Generate tests for each function
            functions.forEach(func => {
                testContent += `  describe('${func}', () => {\n`;
                testContent += `    test('should handle valid input', () => {\n`;
                testContent += `      // Arrange\n`;
                testContent += `      const input = /* provide test input */;\n`;
                testContent += `      const expected = /* expected output */;\n\n`;
                testContent += `      // Act\n`;
                testContent += `      const result = ${func}(input);\n\n`;
                testContent += `      // Assert\n`;
                testContent += `      expect(result).toBe(expected);\n`;
                testContent += `    });\n\n`;

                testContent += `    test('should handle edge cases', () => {\n`;
                testContent += `      // Test with null, undefined, empty values\n`;
                testContent += `      expect(() => ${func}(null)).not.toThrow();\n`;
                testContent += `      expect(() => ${func}(undefined)).not.toThrow();\n`;
                testContent += `    });\n`;
                testContent += `  });\n\n`;
            });

            // Generate tests for each class
            classes.forEach(cls => {
                testContent += `  describe('${cls}', () => {\n`;
                testContent += `    let instance: ${cls};\n\n`;
                testContent += `    beforeEach(() => {\n`;
                testContent += `      instance = new ${cls}();\n`;
                testContent += `    });\n\n`;
                testContent += `    test('should instantiate correctly', () => {\n`;
                testContent += `      expect(instance).toBeInstanceOf(${cls});\n`;
                testContent += `    });\n`;
                testContent += `  });\n\n`;
            });

            if (functions.length === 0 && classes.length === 0) {
                testContent += `  test('module should load correctly', () => {\n`;
                testContent += `    expect(${fileName}).toBeDefined();\n`;
                testContent += `  });\n`;
            }

            testContent += `});\n`;
        }

        return testContent;
    }

    /**
     * Extract function names from code content
     */
    private extractFunctionsFromCode(content: string): string[] {
        const functions: string[] = [];
        const functionRegex = /(?:export\s+)?(?:async\s+)?function\s+(\w+)|(?:export\s+)?const\s+(\w+)\s*=\s*(?:async\s*)?\(|(?:export\s+)?(\w+)\s*:\s*\([^)]*\)\s*=>/g;
        let match;

        while ((match = functionRegex.exec(content)) !== null) {
            const functionName = match[1] || match[2] || match[3];
            if (functionName && !functions.includes(functionName) && functionName !== 'default') {
                functions.push(functionName);
            }
        }

        return functions;
    }

    /**
     * Extract class names from code content
     */
    private extractClassesFromCode(content: string): string[] {
        const classes: string[] = [];
        const classRegex = /(?:export\s+)?class\s+(\w+)/g;
        let match;

        while ((match = classRegex.exec(content)) !== null) {
            const className = match[1];
            if (className && !classes.includes(className)) {
                classes.push(className);
            }
        }

        return classes;
    }

    private getTestFilePath(filePath: string, projectInfo?: any): string {
        const dir = path.dirname(filePath);
        const fileName = path.basename(filePath, path.extname(filePath));
        const extension = path.extname(filePath);

        // Common test file patterns
        const testPatterns = [
            `${fileName}.test${extension}`,
            `${fileName}.spec${extension}`,
            `__tests__/${fileName}${extension}`
        ];

        return path.join(dir, testPatterns[0]);
    }

    private analyzeOptimizations(content: string, filePath: string): any {
        const optimizations: any[] = [];
        const lines = content.split('\n');

        // Basic optimization analysis
        lines.forEach((line, index) => {
            // Check for console.log statements
            if (line.includes('console.log')) {
                optimizations.push({
                    type: 'performance',
                    line: index + 1,
                    issue: 'Console.log statement found',
                    suggestion: 'Remove console.log statements in production code'
                });
            }

            // Check for var declarations
            if (line.trim().startsWith('var ')) {
                optimizations.push({
                    type: 'modernization',
                    line: index + 1,
                    issue: 'var declaration found',
                    suggestion: 'Use const or let instead of var'
                });
            }

            // Check for long lines
            if (line.length > 120) {
                optimizations.push({
                    type: 'readability',
                    line: index + 1,
                    issue: 'Line too long',
                    suggestion: 'Break long lines for better readability'
                });
            }
        });

        return {
            filePath,
            totalIssues: optimizations.length,
            optimizations,
            summary: `Found ${optimizations.length} optimization opportunities`
        };
    }

    private addCodeDocumentation(content: string, filePath: string): string {
        const lines = content.split('\n');
        const documentedLines: string[] = [];

        lines.forEach((line, index) => {
            // Add JSDoc comments for function declarations
            if (line.trim().match(/^(export\s+)?(async\s+)?function\s+\w+/)) {
                documentedLines.push('/**');
                documentedLines.push(' * TODO: Add function description');
                documentedLines.push(' * @param {any} param - Parameter description');
                documentedLines.push(' * @returns {any} Return value description');
                documentedLines.push(' */');
            }

            // Add class documentation
            if (line.trim().match(/^(export\s+)?class\s+\w+/)) {
                documentedLines.push('/**');
                documentedLines.push(' * TODO: Add class description');
                documentedLines.push(' */');
            }

            documentedLines.push(line);
        });

        return documentedLines.join('\n');
    }

    private performCodeQualityAnalysis(content: string, filePath: string): any {
        const lines = content.split('\n');
        const issues: any[] = [];
        const metrics = {
            linesOfCode: lines.length,
            complexity: 0,
            maintainabilityIndex: 0
        };

        lines.forEach((line, index) => {
            const trimmedLine = line.trim();

            // Check for code smells
            if (trimmedLine.includes('TODO') || trimmedLine.includes('FIXME')) {
                issues.push({
                    type: 'maintenance',
                    line: index + 1,
                    severity: 'low',
                    message: 'TODO/FIXME comment found'
                });
            }

            // Check for magic numbers
            const magicNumberRegex = /\b\d{2,}\b/g;
            if (magicNumberRegex.test(trimmedLine) && !trimmedLine.includes('//')) {
                issues.push({
                    type: 'maintainability',
                    line: index + 1,
                    severity: 'medium',
                    message: 'Magic number detected - consider using named constants'
                });
            }

            // Complexity indicators
            if (trimmedLine.includes('if') || trimmedLine.includes('for') || trimmedLine.includes('while')) {
                metrics.complexity++;
            }
        });

        // Calculate maintainability index (simplified)
        metrics.maintainabilityIndex = Math.max(0, 100 - metrics.complexity - (issues.length * 5));

        return {
            filePath,
            metrics,
            issues,
            qualityScore: metrics.maintainabilityIndex,
            recommendations: this.generateQualityRecommendations(issues, metrics)
        };
    }

    private generateQualityRecommendations(issues: any[], metrics: any): string[] {
        const recommendations = [];

        if (metrics.complexity > 10) {
            recommendations.push('Consider breaking down complex functions into smaller, more manageable pieces');
        }

        if (issues.length > 5) {
            recommendations.push('Address code quality issues to improve maintainability');
        }

        if (metrics.linesOfCode > 500) {
            recommendations.push('Consider splitting large files into smaller modules');
        }

        recommendations.push('Add comprehensive unit tests');
        recommendations.push('Consider adding type annotations for better code documentation');

        return recommendations;
    }

    private extractTitle(htmlContent: string): string {
        const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i);
        return titleMatch ? titleMatch[1].trim() : 'No title found';
    }

    /**
     * Perform actual code refactoring based on instructions
     */
    private performCodeRefactoring(content: string, instructions: string, filePath: string): string {
        let refactoredContent = content;
        const extension = path.extname(filePath).toLowerCase();

        // Apply common refactoring patterns based on instructions
        if (instructions.toLowerCase().includes('extract function')) {
            refactoredContent = this.extractFunctions(refactoredContent);
        }

        if (instructions.toLowerCase().includes('improve naming')) {
            refactoredContent = this.improveVariableNaming(refactoredContent);
        }

        if (instructions.toLowerCase().includes('add error handling')) {
            refactoredContent = this.addErrorHandling(refactoredContent, extension);
        }

        if (instructions.toLowerCase().includes('add types') && extension === '.js') {
            refactoredContent = this.addTypeAnnotations(refactoredContent);
        }

        return refactoredContent;
    }

    /**
     * Generate refactoring suggestions based on code analysis
     */
    private generateRefactoringSuggestions(content: string, filePath: string): string[] {
        const suggestions: string[] = [];
        const extension = path.extname(filePath).toLowerCase();
        const lines = content.split('\n');

        // Analyze code for improvement opportunities
        let hasLongFunctions = false;
        let hasRepeatedCode = false;
        let hasPoorNaming = false;
        let lacksErrorHandling = false;

        lines.forEach(line => {
            // Check for long functions (simple heuristic)
            if (line.trim().match(/^(function|const\s+\w+\s*=|class\s+\w+)/)) {
                // This is a simplified check - in reality, you'd track function length
                if (content.split(line)[1]?.split('\n').length > 50) {
                    hasLongFunctions = true;
                }
            }

            // Check for poor naming
            if (line.match(/\b(a|b|c|temp|tmp|data|info|obj)\b/)) {
                hasPoorNaming = true;
            }

            // Check for lack of error handling
            if (line.includes('JSON.parse') || line.includes('fetch') || line.includes('require')) {
                if (!content.includes('try') && !content.includes('catch')) {
                    lacksErrorHandling = true;
                }
            }
        });

        // Check for repeated code patterns
        const codeBlocks = content.split('\n').filter(line => line.trim().length > 10);
        const uniqueBlocks = new Set(codeBlocks);
        if (codeBlocks.length > uniqueBlocks.size * 1.2) {
            hasRepeatedCode = true;
        }

        // Generate suggestions based on analysis
        if (hasLongFunctions) {
            suggestions.push('Break down large functions into smaller, focused functions');
        }

        if (hasRepeatedCode) {
            suggestions.push('Extract common code patterns into reusable functions');
        }

        if (hasPoorNaming) {
            suggestions.push('Improve variable and function naming for better readability');
        }

        if (lacksErrorHandling) {
            suggestions.push('Add proper error handling for async operations and parsing');
        }

        if (extension === '.js') {
            suggestions.push('Consider migrating to TypeScript for better type safety');
        }

        if (!content.includes('/**') && !content.includes('//')) {
            suggestions.push('Add documentation and comments to explain complex logic');
        }

        return suggestions.length > 0 ? suggestions : ['Code looks good! No major refactoring needed.'];
    }

    /**
     * Extract repeated code into functions
     */
    private extractFunctions(content: string): string {
        // This is a simplified implementation
        // In a real scenario, you'd use AST parsing to identify extractable code blocks
        return content.replace(
            /(\s*)(console\.log\([^)]+\);\s*)/g,
            '$1this.logMessage($2);'
        );
    }

    /**
     * Improve variable naming
     */
    private improveVariableNaming(content: string): string {
        return content
            .replace(/\bdata\b/g, 'responseData')
            .replace(/\binfo\b/g, 'information')
            .replace(/\btemp\b/g, 'temporary')
            .replace(/\btmp\b/g, 'temporary')
            .replace(/\bobj\b/g, 'object');
    }

    /**
     * Add error handling to code
     */
    private addErrorHandling(content: string, extension: string): string {
        if (extension === '.js' || extension === '.ts') {
            // Add try-catch around JSON.parse
            content = content.replace(
                /JSON\.parse\(([^)]+)\)/g,
                'this.safeJsonParse($1)'
            );

            // Add error handling for fetch calls
            content = content.replace(
                /fetch\(([^)]+)\)/g,
                'this.safeFetch($1)'
            );
        }

        return content;
    }

    /**
     * Add basic type annotations for JavaScript files
     */
    private addTypeAnnotations(content: string): string {
        return content
            .replace(/function\s+(\w+)\s*\(/g, 'function $1(')
            .replace(/const\s+(\w+)\s*=\s*\(/g, 'const $1 = (')
            .replace(/let\s+(\w+)\s*=/g, 'let $1: any =')
            .replace(/var\s+(\w+)\s*=/g, 'var $1: any =');
    }
}
