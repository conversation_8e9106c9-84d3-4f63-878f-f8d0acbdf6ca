import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Copy, Check } from 'lucide-react';

interface Message {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
    mode: string;
    model?: string;
    error?: boolean;
}

interface MessageListProps {
    messages: Message[];
    isLoading: boolean;
}

export const MessageList: React.FC<MessageListProps> = ({ messages, isLoading }) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const [copiedCode, setCopiedCode] = React.useState<string | null>(null);

    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages, isLoading]);

    const copyToClipboard = async (text: string, id: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopiedCode(id);
            setTimeout(() => setCopiedCode(null), 2000);
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
    };

    const formatTimestamp = (timestamp: number) => {
        return new Date(timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    };

    const renderMessage = (message: Message) => {
        return (
            <div key={message.id} className="message">
                <div className="message-header">
                    <span className="message-role">
                        {message.role === 'user' ? 'You' : 'SIA'}
                    </span>
                    <span className="message-timestamp">
                        {formatTimestamp(message.timestamp)}
                    </span>
                    {message.model && (
                        <span className="message-model">
                            {message.model}
                        </span>
                    )}
                    <span className="message-mode">
                        {message.mode}
                    </span>
                </div>
                <div className={`message-content ${message.role} ${message.error ? 'error' : ''}`}>
                    {message.role === 'user' ? (
                        <div style={{ whiteSpace: 'pre-wrap' }}>{message.content}</div>
                    ) : (
                        <ReactMarkdown
                            components={{
                                code({ node, inline, className, children, ...props }) {
                                    const match = /language-(\w+)/.exec(className || '');
                                    const codeId = `code-${message.id}-${Math.random()}`;
                                    
                                    if (!inline && match) {
                                        const codeContent = String(children).replace(/\n$/, '');
                                        return (
                                            <div style={{ position: 'relative' }}>
                                                <SyntaxHighlighter
                                                    style={vscDarkPlus as any}
                                                    language={match[1]}
                                                    PreTag="div"
                                                    {...props}
                                                >
                                                    {codeContent}
                                                </SyntaxHighlighter>
                                                <button
                                                    className="code-copy-btn"
                                                    onClick={() => copyToClipboard(codeContent, codeId)}
                                                    title="Copy code"
                                                >
                                                    {copiedCode === codeId ? (
                                                        <Check size={12} />
                                                    ) : (
                                                        <Copy size={12} />
                                                    )}
                                                </button>
                                            </div>
                                        );
                                    }
                                    
                                    return (
                                        <code className={className} {...props}>
                                            {children}
                                        </code>
                                    );
                                },
                                pre({ children }) {
                                    return <>{children}</>;
                                }
                            }}
                        >
                            {message.content}
                        </ReactMarkdown>
                    )}
                </div>
            </div>
        );
    };

    return (
        <>
            {messages.map(renderMessage)}
            {isLoading && (
                <div className="message">
                    <div className="message-header">
                        <span className="message-role">SIA</span>
                        <span className="message-timestamp">
                            {formatTimestamp(Date.now())}
                        </span>
                    </div>
                    <div className="message-content assistant">
                        <div className="loading">
                            <div className="loading-spinner" />
                            <span>Thinking...</span>
                        </div>
                    </div>
                </div>
            )}
            <div ref={messagesEndRef} />
        </>
    );
};
