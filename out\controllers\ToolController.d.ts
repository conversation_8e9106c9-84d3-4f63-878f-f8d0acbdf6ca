import * as vscode from 'vscode';
import { ToolDefinition, ToolResult } from '../types/tools';
import { WebviewPanelProvider } from '../providers/WebviewPanelProvider';
import { SidePanelProvider } from '../providers/SidePanelProvider';
export declare class ToolController {
    private context;
    private webviewProvider?;
    private tools;
    private toolDefinitions;
    private errorHandler;
    constructor(context: vscode.ExtensionContext);
    setWebviewProvider(provider: WebviewPanelProvider | SidePanelProvider): void;
    /**
     * Register all available tools
     */
    private registerAllTools;
    /**
     * Register a tool with its implementation and definition
     */
    private registerTool;
    /**
     * Execute a tool by name
     */
    executeTool(toolName: string, parameters: any): Promise<ToolResult>;
    /**
     * Get all tool definitions for LLM function calling
     */
    getToolDefinitions(): ToolDefinition[];
    private readFile;
    private writeFile;
    private createFile;
    private readDirectory;
    private createDirectory;
    private moveFile;
    private deleteFile;
    private fileExists;
    private searchInFile;
    private replaceInFile;
    private getCodebaseAST;
    private findSymbolDefinition;
    private getProjectInfo;
    private runInTerminal;
    private runTests;
    private webSearch;
    private fetchWebpage;
    private githubSearch;
    private packageSearch;
    private installPackage;
    private lintCode;
    private formatCode;
    private showDiffForApproval;
    private askUserForInput;
    /**
     * Handle user responses to questions and diff approvals
     */
    handleUserResponse(type: 'diffApproval' | 'userInput', payload: any): Promise<ToolResult>;
    private generateTests;
    private refactorCode;
    private optimizeCode;
    private addDocumentation;
    private analyzeCodeQuality;
    private generateTestContent;
    private generateJSTestContent;
    /**
     * Extract function names from code content
     */
    private extractFunctionsFromCode;
    /**
     * Extract class names from code content
     */
    private extractClassesFromCode;
    private getTestFilePath;
    private analyzeOptimizations;
    private addCodeDocumentation;
    private performCodeQualityAnalysis;
    private generateQualityRecommendations;
    private extractTitle;
    /**
     * Perform actual code refactoring based on instructions
     */
    private performCodeRefactoring;
    /**
     * Generate refactoring suggestions based on code analysis
     */
    private generateRefactoringSuggestions;
    /**
     * Extract repeated code into functions
     */
    private extractFunctions;
    /**
     * Improve variable naming
     */
    private improveVariableNaming;
    /**
     * Add error handling to code
     */
    private addErrorHandling;
    /**
     * Add basic type annotations for JavaScript files
     */
    private addTypeAnnotations;
}
//# sourceMappingURL=ToolController.d.ts.map