// SIA AI Assistant - <PERSON>illa JS Webview
class SIAWebview {
    constructor() {
        this.vscode = acquireVsCodeApi();
        this.currentMode = 'chat';
        this.messages = [];
        this.isLoading = false;
        this.apiKeyStatus = {};
        
        this.initializeElements();
        this.setupEventListeners();
        this.setupMessageListener();
        this.requestInitialData();
    }

    initializeElements() {
        // Main elements
        this.chatArea = document.getElementById('chatArea');
        this.messagesContainer = document.getElementById('messages');
        this.emptyState = document.getElementById('emptyState');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.modeSelector = document.getElementById('modeSelector');
        
        // Header elements
        this.newChatBtn = document.querySelector('.new-chat-btn');
        this.settingsBtn = document.querySelector('.settings-btn');
        this.headerTitle = document.querySelector('.header-title');
        
        // Settings elements
        this.settingsPanel = document.getElementById('settingsPanel');
        this.closeSettingsBtn = document.getElementById('closeSettingsBtn');
        this.providerSelector = document.getElementById('providerSelector');
        this.modelSelector = document.getElementById('modelSelector');
        
        // Tab elements
        this.tabs = document.querySelectorAll('.tab');
        
        // API key elements
        this.apiKeyInputs = document.querySelectorAll('.api-key-input[data-provider]');
        this.apiKeyStatuses = document.querySelectorAll('.api-key-status[data-provider]');
        this.saveBtns = document.querySelectorAll('.save-btn[data-provider]');
    }

    setupEventListeners() {
        // Send message
        this.sendBtn.addEventListener('click', () => this.handleSendMessage());
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.handleSendMessage();
            }
        });

        // Mode selector
        this.modeSelector.addEventListener('change', (e) => {
            this.currentMode = e.target.value;
            this.updateHeaderTitle();
            this.vscode.postMessage({
                type: 'modeChanged',
                mode: this.currentMode
            });
        });

        // Header buttons
        this.newChatBtn.addEventListener('click', () => this.handleNewChat());
        this.settingsBtn.addEventListener('click', () => this.showSettings());

        // Settings panel
        this.closeSettingsBtn.addEventListener('click', () => this.hideSettings());

        // API key management
        this.saveBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const provider = e.target.dataset.provider;
                this.handleSaveApiKey(provider);
            });
        });

        // Provider/Model selection
        this.providerSelector.addEventListener('change', () => this.updateModelOptions());
        this.modelSelector.addEventListener('change', () => this.handleModelChange());

        // Tabs
        this.tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.handleTabChange(e.target.dataset.tab);
            });
        });

        // Auto-resize textarea
        this.messageInput.addEventListener('input', () => this.autoResizeTextarea());
    }

    setupMessageListener() {
        window.addEventListener('message', (event) => {
            const message = event.data;
            this.handleExtensionMessage(message);
        });
    }

    requestInitialData() {
        // Request API key status and current settings
        this.vscode.postMessage({ type: 'getApiKeys' });
        this.vscode.postMessage({ type: 'getCurrentModel' });
    }

    handleExtensionMessage(message) {
        switch (message.type) {
            case 'chatResponse':
                this.handleChatResponse(message.content, message.mode);
                break;
            case 'chatCleared':
                this.handleChatCleared();
                break;
            case 'settingsShown':
                this.showSettings();
                break;
            case 'apiKeys':
                this.updateApiKeyStatus(message.payload);
                break;
            case 'currentModel':
                this.updateCurrentModel(message.payload);
                break;
            case 'error':
                this.showError(message.error);
                break;
            case 'planGenerated':
                this.handlePlanGenerated(message.plan);
                break;
            case 'planApproved':
                this.handlePlanApproved();
                break;
            case 'stepCompleted':
                this.handleStepCompleted(message.step, message.result);
                break;
            case 'agentThought':
                this.handleAgentThought(message.thought);
                break;
        }
    }

    handleSendMessage() {
        const content = this.messageInput.value.trim();
        if (!content || this.isLoading) return;

        // Add user message to UI
        this.addMessage('user', content);
        
        // Clear input
        this.messageInput.value = '';
        this.autoResizeTextarea();

        // Hide empty state
        this.emptyState.style.display = 'none';

        // Set loading state
        this.setLoading(true);

        // Send to extension
        this.vscode.postMessage({
            type: 'userQuery',
            query: content,
            mode: this.currentMode
        });
    }

    handleChatResponse(content, mode) {
        this.setLoading(false);
        this.addMessage('assistant', content, mode);
    }

    handleChatCleared() {
        this.messages = [];
        this.messagesContainer.innerHTML = '';
        this.emptyState.style.display = 'flex';
    }

    handleNewChat() {
        this.vscode.postMessage({ type: 'newChat' });
    }

    addMessage(role, content, mode = null) {
        const message = { role, content, timestamp: Date.now(), mode };
        this.messages.push(message);

        const messageElement = this.createMessageElement(message);
        this.messagesContainer.appendChild(messageElement);
        
        // Scroll to bottom
        this.chatArea.scrollTop = this.chatArea.scrollHeight;
    }

    createMessageElement(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.role}`;

        const headerDiv = document.createElement('div');
        headerDiv.className = 'message-header';

        const roleSpan = document.createElement('span');
        roleSpan.className = `message-role ${message.role}`;
        roleSpan.textContent = message.role === 'user' ? 'You' : 'SIA';

        if (message.mode) {
            const modeSpan = document.createElement('span');
            modeSpan.className = 'message-mode';
            modeSpan.textContent = `(${message.mode})`;
            modeSpan.style.color = '#8c8c8c';
            modeSpan.style.fontSize = '11px';
            headerDiv.appendChild(roleSpan);
            headerDiv.appendChild(modeSpan);
        } else {
            headerDiv.appendChild(roleSpan);
        }

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = message.content;

        messageDiv.appendChild(headerDiv);
        messageDiv.appendChild(contentDiv);

        return messageDiv;
    }

    setLoading(loading) {
        this.isLoading = loading;
        this.sendBtn.disabled = loading;
        
        if (loading) {
            this.sendBtn.innerHTML = '<div class="loading-spinner"></div>';
        } else {
            this.sendBtn.innerHTML = '➤';
        }
    }

    showSettings() {
        this.settingsPanel.classList.add('active');
    }

    hideSettings() {
        this.settingsPanel.classList.remove('active');
    }

    handleSaveApiKey(provider) {
        const input = document.querySelector(`.api-key-input[data-provider="${provider}"]`);
        const key = input.value.trim();
        
        if (!key) {
            this.showError('API key cannot be empty');
            return;
        }

        // Send to extension
        this.vscode.postMessage({
            type: 'setApiKey',
            payload: { provider, key }
        });

        // Clear input
        input.value = '';
    }

    updateApiKeyStatus(status) {
        this.apiKeyStatus = status;
        
        Object.keys(status).forEach(provider => {
            const statusElement = document.querySelector(`.api-key-status[data-provider="${provider}"]`);
            if (statusElement) {
                statusElement.className = `api-key-status ${status[provider] ? 'valid' : 'invalid'}`;
            }
        });
    }

    updateCurrentModel(modelInfo) {
        if (modelInfo.provider) {
            this.providerSelector.value = modelInfo.provider;
        }
        if (modelInfo.model) {
            this.updateModelOptions();
            this.modelSelector.value = modelInfo.model;
        }
    }

    updateModelOptions() {
        const provider = this.providerSelector.value;
        const modelOptions = {
            gemini: [
                { value: 'gemini-2.0-flash-exp', text: 'Gemini 2.0 Flash' },
                { value: 'gemini-1.5-flash', text: 'Gemini 1.5 Flash' }
            ],
            openai: [
                { value: 'gpt-4o', text: 'GPT-4o' },
                { value: 'gpt-4-turbo', text: 'GPT-4 Turbo' },
                { value: 'gpt-3.5-turbo', text: 'GPT-3.5 Turbo' }
            ],
            anthropic: [
                { value: 'claude-3-5-sonnet-20241022', text: 'Claude 3.5 Sonnet' },
                { value: 'claude-3-opus-20240229', text: 'Claude 3 Opus' },
                { value: 'claude-3-haiku-20240307', text: 'Claude 3 Haiku' }
            ],
            mistral: [
                { value: 'mistral-large-latest', text: 'Mistral Large' },
                { value: 'mistral-medium-latest', text: 'Mistral Medium' }
            ],
            deepseek: [
                { value: 'deepseek-chat', text: 'DeepSeek Chat' },
                { value: 'deepseek-coder', text: 'DeepSeek Coder' }
            ]
        };

        const options = modelOptions[provider] || [];
        this.modelSelector.innerHTML = '';
        
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            this.modelSelector.appendChild(optionElement);
        });
    }

    handleModelChange() {
        const provider = this.providerSelector.value;
        const model = this.modelSelector.value;
        
        this.vscode.postMessage({
            type: 'setModel',
            payload: { provider, model }
        });
    }

    updateHeaderTitle() {
        const modeNames = {
            chat: 'CHAT',
            agent: 'AGENT',
            'auto-agent': 'AUTO-AGENT'
        };
        this.headerTitle.textContent = `SIA: ${modeNames[this.currentMode]}`;
    }

    handleTabChange(tab) {
        // Update active tab
        this.tabs.forEach(t => t.classList.remove('active'));
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');
        
        // Handle tab-specific logic
        this.vscode.postMessage({
            type: 'tabChanged',
            tab: tab
        });
    }

    autoResizeTextarea() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    showError(error) {
        // Create a temporary error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #f14c4c;
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;
        errorDiv.textContent = error;
        document.body.appendChild(errorDiv);

        // Remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    // Agent mode specific handlers
    handlePlanGenerated(plan) {
        this.addMessage('assistant', `Plan Generated:\n\n${plan.description}\n\nSteps:\n${plan.steps.map((step, i) => `${i + 1}. ${step.description}`).join('\n')}\n\nApprove this plan?`, 'agent');
    }

    handlePlanApproved() {
        this.addMessage('assistant', 'Plan approved. Executing steps...', 'agent');
    }

    handleStepCompleted(step, result) {
        this.addMessage('assistant', `Step ${step.stepNumber} completed: ${step.description}\nResult: ${result.message}`, 'agent');
    }

    handleAgentThought(thought) {
        this.addMessage('assistant', `[THOUGHT] ${thought}`, 'auto-agent');
    }
}

// Initialize the webview when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SIAWebview();
});
