export interface ToolDefinition {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: {
            [key: string]: any;
        };
        required: string[];
    };
}
export interface ToolResult {
    success: boolean;
    data?: any;
    error?: string;
    requiresApproval?: boolean;
    diffId?: string;
}
export interface FileSystemTool {
    readFile(path: string): Promise<ToolResult>;
    writeFile(path: string, content: string): Promise<ToolResult>;
    createFile(path: string): Promise<ToolResult>;
    moveFile(source: string, destination: string): Promise<ToolResult>;
    readDirectory(path: string): Promise<ToolResult>;
    createDirectory(path: string): Promise<ToolResult>;
    deleteFile(path: string): Promise<ToolResult>;
    fileExists(path: string): Promise<ToolResult>;
    directoryExists(path: string): Promise<ToolResult>;
}
export interface CodeIntelligenceTool {
    getCodebaseAST(globPattern: string): Promise<ToolResult>;
    findSymbolDefinition(symbolName: string): Promise<ToolResult>;
    findSymbolReferences(symbolName: string): Promise<ToolResult>;
    semanticSearch(query: string): Promise<ToolResult>;
    getProjectInfo(): Promise<ToolResult>;
    analyzeCode(filePath: string): Promise<ToolResult>;
}
export interface ExecutionTool {
    runInTerminal(command: string, description?: string): Promise<ToolResult>;
    getTerminalOutput(terminalId?: string): Promise<ToolResult>;
    runTests(testCommand?: string): Promise<ToolResult>;
    generateTestForFile(filePath: string): Promise<ToolResult>;
    lintCheck(filePath?: string): Promise<ToolResult>;
    getErrors(): Promise<ToolResult>;
}
export interface WebTool {
    webSearch(query: string): Promise<ToolResult>;
    fetchWebpage(url: string): Promise<ToolResult>;
    githubSearch(query: string): Promise<ToolResult>;
}
export interface UserInteractionTool {
    showDiffForApproval(filePath: string, newContent: string): Promise<ToolResult>;
    askUserForInput(question: string): Promise<ToolResult>;
    showNotification(message: string, type?: 'info' | 'warning' | 'error'): Promise<ToolResult>;
}
export interface WorkflowTool {
    refactorCode(filePath: string, instructions: string): Promise<ToolResult>;
    optimizeCode(filePath: string): Promise<ToolResult>;
    extractFunction(filePath: string, startLine: number, endLine: number, functionName: string): Promise<ToolResult>;
    removeDuplication(filePath: string): Promise<ToolResult>;
    translateCode(filePath: string, targetLanguage: string): Promise<ToolResult>;
}
export type AllTools = FileSystemTool & CodeIntelligenceTool & ExecutionTool & WebTool & UserInteractionTool & WorkflowTool;
//# sourceMappingURL=tools.d.ts.map